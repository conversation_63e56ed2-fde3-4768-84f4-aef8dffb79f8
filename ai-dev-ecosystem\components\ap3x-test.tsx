"use client"

import React, { useState } from 'react';
import { ap3xClient } from '@/lib/ap3x-client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, AlertCircle, Sparkles, Send } from 'lucide-react';

export default function AP3XTest() {
  const [testResult, setTestResult] = useState<any>(null);
  const [isTesting, setIsTesting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [featureRequest, setFeatureRequest] = useState('');
  const [appId, setAppId] = useState('1');

  const testConnection = async () => {
    try {
      setIsTesting(true);
      setError(null);
      const health = await ap3xClient.getHealth();
      setTestResult(health);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Test failed');
    } finally {
      setIsTesting(false);
    }
  };

  const testProjectPlan = async () => {
    try {
      setIsTesting(true);
      setError(null);
      const plan = await ap3xClient.createProjectPlan(parseInt(appId), featureRequest || 'Build a modern web application');
      setTestResult(plan);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Test failed');
    } finally {
      setIsTesting(false);
    }
  };

  const testTaskPlan = async () => {
    try {
      setIsTesting(true);
      setError(null);
      const plan = await ap3xClient.createTaskPlan(parseInt(appId), featureRequest || 'Add user authentication');
      setTestResult(plan);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Test failed');
    } finally {
      setIsTesting(false);
    }
  };

  const testAgentMessage = async () => {
    try {
      setIsTesting(true);
      setError(null);
      const message = await ap3xClient.sendAgentMessage(parseInt(appId), featureRequest || 'Hello AP3X!');
      setTestResult(message);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Test failed');
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <div className="space-y-4">
      <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-500" />
            AP3X Test Suite
          </CardTitle>
          <CardDescription className="text-[#666]">
            Test AP3X autonomous development capabilities
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="app-id">App ID</Label>
            <Input
              id="app-id"
              value={appId}
              onChange={(e) => setAppId(e.target.value)}
              placeholder="1"
              className="bg-[#1a1a1a] border-[#333] text-white"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="feature-request">Feature Request</Label>
            <Textarea
              id="feature-request"
              value={featureRequest}
              onChange={(e) => setFeatureRequest(e.target.value)}
              placeholder="Describe what you want to build..."
              className="bg-[#1a1a1a] border-[#333] text-white"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={testConnection}
              disabled={isTesting}
              variant="outline"
              className="bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]"
            >
              {isTesting ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Test Connection'}
            </Button>
            <Button
              onClick={testProjectPlan}
              disabled={isTesting}
              variant="outline"
              className="bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]"
            >
              {isTesting ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Test Project Plan'}
            </Button>
            <Button
              onClick={testTaskPlan}
              disabled={isTesting}
              variant="outline"
              className="bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]"
            >
              {isTesting ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Test Task Plan'}
            </Button>
            <Button
              onClick={testAgentMessage}
              disabled={isTesting}
              variant="outline"
              className="bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]"
            >
              {isTesting ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Test Agent'}
            </Button>
          </div>

          {error && (
            <Alert variant="destructive" className="bg-red-900/20 border-red-900/50">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-red-400">{error}</AlertDescription>
            </Alert>
          )}

          {testResult && (
            <Card className="bg-[#111111] border-[#1a1a1a]">
              <CardHeader>
                <CardTitle className="text-sm text-white flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  Test Result
                </CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs text-[#666] overflow-auto">
                  {JSON.stringify(testResult, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
