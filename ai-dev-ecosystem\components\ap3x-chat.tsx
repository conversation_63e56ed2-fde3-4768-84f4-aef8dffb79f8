"use client"

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useDyadChats, useDyadModels, useDyadStreaming } from '@/hooks/use-dyad';
import { ap3xClient, AP3XTask, AgentMessage } from '@/lib/ap3x-client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';
import { 
  ArrowUp, 
  Bot, 
  User, 
  Loader2, 
  MessageSquare,
  Settings,
  Plus,
  Trash2,
  CheckCircle,
  Clock,
  AlertCircle,
  Sparkles,
  Code,
  FileText,
  Zap
} from 'lucide-react';
import { App, Chat, ChatMessage } from '@/lib/dyad-client';
import { useSocket } from '@/hooks/use-socket';

interface AP3XChatProps {
  selectedApp?: App;
}

export default function AP3XChat({ selectedApp }: AP3XChatProps) {
  const { chats, createChat, deleteChat, addMessage } = useDyadChats(selectedApp?.id);
  const { models } = useDyadModels();
  const { isStreaming, streamContent, startStream, resetStream } = useDyadStreaming();
  
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [chatMessages, setChatMessages] = useState<AgentMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [selectedModel, setSelectedModel] = useState('moonshotai/kimi-k2');
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [tasks, setTasks] = useState<AP3XTask[]>([]);
  const [activeAgents, setActiveAgents] = useState<string[]>([]);
  const [agentStatus, setAgentStatus] = useState<string>('ready');
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Real-time updates via Socket.IO
  useSocket({
    onTaskUpdated: (update) => {
      if (update.appId === selectedApp?.id) {
        setTasks(prev => prev.map(task => 
          task.id === update.taskId ? { ...task, ...update } : task
        ));
        
        // Add agent message about task update
        const taskMessage: AgentMessage = {
          id: Date.now().toString(),
          type: 'task-update',
          content: `Task "${update.title}" ${update.status === 'completed' ? 'completed' : 'updated'}`,
          metadata: {
            taskId: update.taskId,
            progress: update.progress,
            status: update.status
          },
          timestamp: new Date().toISOString(),
          agentName: update.agentName || 'AP3X Agent'
        };
        
        setChatMessages(prev => [...prev, taskMessage]);
      }
    },
    onAgentMessage: (message) => {
      if (message.appId === selectedApp?.id) {
        setChatMessages(prev => [...prev, message]);
      }
    }
  });

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages, streamContent]);

  // Load agent conversation and tasks
  useEffect(() => {
    if (selectedApp) {
      loadAgentConversation();
      loadTasks();
      subscribeToAgentUpdates();
    }
  }, [selectedApp]);

  const loadAgentConversation = async () => {
    if (!selectedApp) return;
    try {
      const conversation = await ap3xClient.getAgentConversation(selectedApp.id);
      setChatMessages(conversation);
    } catch (error) {
      console.error('Failed to load agent conversation:', error);
    }
  };

  const loadTasks = async () => {
    if (!selectedApp) return;
    try {
      const { tasks } = await ap3xClient.getTaskUpdates(selectedApp.id);
      setTasks(tasks);
    } catch (error) {
      console.error('Failed to load tasks:', error);
    }
  };

  const subscribeToAgentUpdates = () => {
    if (!selectedApp) return;
    
    ap3xClient.streamAgentUpdates(selectedApp.id, (update) => {
      if (update.type === 'task-update') {
        setTasks(prev => prev.map(task => 
          task.id === update.taskId ? { ...task, ...update } : task
        ));
      } else if (update.type === 'agent-message') {
        setChatMessages(prev => [...prev, update]);
      }
    });
  };

  const handleCreateChat = async () => {
    if (!selectedApp) return;
    
    try {
      const newChat = await createChat(selectedApp.id, `AP3X Session ${chats.length + 1}`);
      setSelectedChat(newChat);
      
      // Initialize AP3X agent
      const welcomeMessage: AgentMessage = {
        id: Date.now().toString(),
        type: 'agent',
        content: `Hello! I'm your AP3X development assistant. I'll help you build ${selectedApp.name} step by step. I can create tasks, write code, and provide real-time guidance. What would you like to work on first?`,
        timestamp: new Date().toISOString(),
        agentName: 'AP3X Assistant'
      };
      
      setChatMessages([welcomeMessage]);
    } catch (err) {
      console.error('Failed to create chat:', err);
    }
  };

  const handleSendMessage = async () => {
    if (!chatInput.trim() || !selectedApp) return;

    const userMessage: AgentMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: chatInput,
      timestamp: new Date().toISOString()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');

    try {
      // Send to AP3X agent
      const agentResponse = await ap3xClient.sendAgentMessage(selectedApp.id, chatInput);
      setChatMessages(prev => [...prev, agentResponse]);
      
      // Update tasks if agent created new ones
      const { tasks: updatedTasks } = await ap3xClient.getTaskUpdates(selectedApp.id);
      setTasks(updatedTasks);
      
    } catch (err) {
      console.error('Failed to send message:', err);
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'feature': return <Sparkles className="w-4 h-4" />;
      case 'bugfix': return <AlertCircle className="w-4 h-4" />;
      case 'refactor': return <Code className="w-4 h-4" />;
      case 'documentation': return <FileText className="w-4 h-4" />;
      default: return <Zap className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400';
      case 'in-progress': return 'text-blue-400';
      case 'blocked': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!selectedApp) {
    return (
      <div className="h-full flex items-center justify-center text-[#666]">
        <div className="text-center">
          <Bot className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>Select an app to start collaborating with AP3X</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-[#0a0a0a] rounded-xl overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
          <h2 className="font-medium text-white text-sm">
            {selectedApp.name} - AP3X Assistant
          </h2>
          {activeAgents.length > 0 && (
            <Badge variant="secondary" className="bg-purple-900/20 text-purple-400 border-purple-800">
              {activeAgents.length} active
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleCreateChat}
            className="h-7 px-2 bg-[#1a1a1a] border-[#333] text-[#888] hover:text-white hover:bg-[#2a2a2a]"
          >
            <Plus className="w-3 h-3 mr-1" />
            New Session
          </Button>
          <Select value={selectedModel} onValueChange={setSelectedModel}>
            <SelectTrigger className="w-48 h-7 bg-[#1a1a1a] border-[#333] text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-[#1a1a1a] border-[#333]">
              {models
                .filter(model => model.provider === 'openrouter')
                .slice(0, 10)
                .map((model) => (
                  <SelectItem key={model.name} value={model.name} className="text-xs">
                    {model.displayName || model.name}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex-1 flex min-h-0">
        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <ScrollArea className="flex-1">
            <div className="px-6 py-4 space-y-4">
              {chatMessages.map((message) => (
                <div key={message.id} className="group">
                  <div className={`flex gap-3 ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                    <div className="flex-shrink-0">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                          message.type === 'user'
                            ? 'bg-purple-600 text-white'
                            : message.type === 'agent'
                            ? 'bg-purple-900/50 text-purple-300 border border-purple-700'
                            : message.type === 'task-update'
                            ? 'bg-green-900/50 text-green-300 border border-green-700'
                            : 'bg-[#1a1a1a] text-[#888] border border-[#2a2a2a]'
                        }`}
                      >
                        {message.type === 'user' ? (
                          <User className="w-4 h-4" />
                        ) : message.type === 'agent' ? (
                          <Bot className="w-4 h-4" />
                        ) : (
                          <Sparkles className="w-4 h-4" />
                        )}
                      </div>
                    </div>
                    <div className={`flex-1 max-w-[85%] ${message.type === 'user' ? 'text-right' : ''}`}>
                      <div
                        className={`inline-block px-4 py-3 rounded-lg text-sm leading-relaxed ${
                          message.type === 'user'
                            ? 'bg-purple-600 text-white shadow-lg'
                            : message.type === 'agent'
                            ? 'bg-purple-900/30 text-purple-200 border border-purple-700/50 shadow-md'
                            : message.type === 'task-update'
                            ? 'bg-green-900/30 text-green-200 border border-green-700/50 shadow-md'
                            : 'bg-[#111111] text-[#e5e5e5] shadow-md'
                        }`}
                      >
                        <div className="whitespace-pre-wrap">{message.content}</div>
                        {message.metadata?.progress !== undefined && (
                          <div className="mt-2">
                            <Progress value={message.metadata.progress} className="h-1" />
                          </div>
                        )}
                      </div>
                      <div className={`text-xs text-[#666] mt-1 ${message.type === 'user' ? 'text-right' : ''}`}>
                        {formatTime(message.timestamp)}
                        {message.agentName && (
                          <span className="ml-2 text-purple-400">@{message.agentName}</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {/* Streaming Response */}
              {isStreaming && streamContent && (
                <div className="group">
                  <div className="flex gap-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium bg-purple-900/50 text-purple-300 border border-purple-700">
                        <Bot className="w-4 h-4" />
                      </div>
                    </div>
                    <div className="flex-1 max-w-[85%]">
                      <div className="inline-block px-4 py-3 rounded-lg text-sm leading-relaxed bg-purple-900/30 text-purple-200 border border-purple-700/50 shadow-md">
                        <div className="whitespace-pre-wrap">{streamContent}</div>
                        <div className="inline-block w-2 h-4 bg-purple-500 animate-pulse ml-1"></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Chat Input */}
          <div className="shrink-0 p-3 bg-[#0a0a0a] shadow-inner">
            <div className="relative bg-[#111111] rounded-xl p-1 focus-within:shadow-purple-500/20 focus-within:shadow-lg transition-all shadow-md">
              <textarea
                ref={textareaRef}
                value={chatInput}
                onChange={(e) => {
                  setChatInput(e.target.value);
                  const textarea = e.target as HTMLTextAreaElement;
                  textarea.style.height = 'auto';
                  const scrollHeight = textarea.scrollHeight;
                  const maxHeight = 240;
                  textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';
                }}
                onKeyDown={handleKeyDown}
                placeholder="Ask AP3X to help you build something amazing..."
                className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none leading-6 scrollbar-none overflow-y-auto"
                rows={3}
                style={{ minHeight: '84px', maxHeight: '240px' }}
                disabled={isStreaming}
              />
              <button
                onClick={handleSendMessage}
                disabled={!chatInput.trim() || isStreaming}
                className="absolute right-2 top-3 w-8 h-8 bg-purple-600 hover:bg-purple-700 disabled:bg-[#333] disabled:text-[#666] rounded-lg flex items-center justify-center transition-colors shadow-md"
              >
                {isStreaming ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <ArrowUp className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Task Sidebar */}
        <div className="w-80 border-l border-[#1a1a1a] bg-[#0a0a0a]">
          <div className="p-4 border-b border-[#1a1a1a]">
            <h3 className="text-sm font-medium text-white flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-purple-400" />
              AP3X Tasks
            </h3>
            <p className="text-xs text-[#666] mt-1">
              {tasks.filter(t => t.status === 'completed').length} / {tasks.length} completed
            </p>
          </div>
          
          <ScrollArea className="h-[calc(100%-120px)]">
            <div className="p-4 space-y-3">
              {tasks.length === 0 ? (
                <div className="text-center text-[#666] py-8">
                  <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No tasks yet</p>
                  <p className="text-xs mt-1">Ask AP3X to start planning</p>
                </div>
              ) : (
                tasks.map((task) => (
                  <Card key={task.id} className="bg-[#111111] border-[#1a1a1a] p-3">
                    <div className="flex items-start gap-2">
                      <div className="mt-0.5">
                        {getTaskIcon(task.type)}
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-white">{task.title}</h4>
                        <p className="text-xs text-[#666] mt-1">{task.description}</p>
                        
                        <div className="mt-2">
                          <div className="flex items-center justify-between text-xs">
                            <span className={getStatusColor(task.status)}>
                              {task.status}
                            </span>
                            <span className="text-[#666]">{task.estimatedHours}h</span>
                          </div>
                          
                          {task.status === 'in-progress' && (
                            <div className="mt-2">
                              <Progress value={task.progress} className="h-1" />
                              <p className="text-xs text-[#666] mt-1">
                                {task.progress}% complete
                              </p>
                            </div>
                          )}
                          
                          {task.agentUpdates.length > 0 && (
                            <div className="mt-2">
                              <p className="text-xs text-purple-400">
                                💡 {task.agentUpdates[task.agentUpdates.length - 1]}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
}
