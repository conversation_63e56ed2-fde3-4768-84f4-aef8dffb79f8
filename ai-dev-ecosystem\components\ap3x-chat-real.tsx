"use client"

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Bot, User, Plus, CheckCircle, Clock, AlertCircle, Sparkles, Code, FileText, Zap, Target, Users, Send, X, Brain, Activity } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { ap3xClient } from '@/lib/ap3x-client';

interface AP3XChatRealProps {
  selectedApp?: any;
}

interface AgentPersonality {
  name: string;
  tone: string;
  greeting: string;
  avatar: string;
  color: string;
  personality: string;
}

// Enhanced agent personalities
const agentPersonalities: Record<string, AgentPersonality> = {
  collaborative: {
    name: 'Collaborative Partner',
    tone: 'friendly and supportive',
    greeting: "Hey there! I'm your development partner. Let's build something amazing together! 🤝 I'll be here every step of the way, offering suggestions and keeping you updated on our progress.",
    avatar: '🤝',
    color: 'purple',
    personality: 'Supportive and encouraging, always ready to brainstorm and provide constructive feedback.'
  },
  expert: {
    name: 'Expert Consultant',
    tone: 'professional and precise',
    greeting: "Greetings! I'm here to provide expert guidance and ensure we build with best practices. 🎯 I'll analyze your requirements and suggest optimal solutions based on industry standards.",
    avatar: '🎯',
    color: 'blue',
    personality: 'Analytical and methodical, focused on best practices and technical excellence.'
  },
  creative: {
    name: 'Creative Collaborator',
    tone: 'innovative and enthusiastic',
    greeting: "What's up! Ready to create something innovative? Let's think outside the box! ✨ I'll help you explore creative solutions and modern approaches.",
    avatar: '✨',
    color: 'pink',
    personality: 'Innovative and enthusiastic, always exploring new possibilities and creative solutions.'
  },
  mentor: {
    name: 'Mentor Guide',
    tone: 'patient and educational',
    greeting: "Hello! I'll guide you through each step and help you learn along the way. 📚 I'll explain my decisions and teach you the reasoning behind each approach.",
    avatar: '📚',
    color: 'green',
    personality: 'Patient and educational, focused on teaching and explaining the why behind technical decisions.'
  }
};

export default function AP3XChatReal({ selectedApp }: AP3XChatRealProps) {
  const [chatMessages, setChatMessages] = useState<any[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [tasks, setTasks] = useState<any[]>([]);
  const [activeAgents, setActiveAgents] = useState<any[]>([]);
  const [agentPersonality, setAgentPersonality] = useState('collaborative');
  const [showTaskPanel, setShowTaskPanel] = useState(true);
  const [showAgentPanel, setShowAgentPanel] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  // Load initial data
  useEffect(() => {
    if (selectedApp) {
      loadInitialData();
      setupRealTimeUpdates();
    }
    
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [selectedApp]);

  const loadInitialData = async () => {
    if (!selectedApp) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Load tasks
      const taskData = await ap3xClient.getTaskUpdates(selectedApp.id);
      setTasks(taskData.tasks);
      
      // Load agents
      const agents = await ap3xClient.getAgents();
      setActiveAgents(agents);
      
      // Load conversation history
      const conversation = await ap3xClient.getAgentConversation(selectedApp.id);
      setChatMessages(conversation);
      
      // Add welcome message if empty
      if (conversation.length === 0) {
        const personality = agentPersonalities[agentPersonality as keyof typeof agentPersonalities];
        const welcomeMessage = {
          id: Date.now().toString(),
          type: 'agent',
          content: personality.greeting,
          timestamp: new Date().toISOString(),
          agentName: personality.name
        };
        setChatMessages([welcomeMessage]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
      console.error('Error loading initial data:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const setupRealTimeUpdates = () => {
    if (!selectedApp) return;
    
    // Setup SSE for real-time updates
    const eventSource = new EventSource(`http://localhost:3002/api/ap3x/stream/${selectedApp.id}`);
    eventSourceRef.current = eventSource;
    
    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        handleRealTimeUpdate(data);
      } catch (err) {
        console.error('Error parsing SSE data:', err);
      }
    };
    
    eventSource.onerror = (error) => {
      console.error('SSE error:', error);
      // Reconnect after 3 seconds
      setTimeout(() => {
        if (selectedApp) {
          setupRealTimeUpdates();
        }
      }, 3000);
    };
  };

  const handleRealTimeUpdate = (update: any) => {
    switch (update.type) {
      case 'taskUpdated':
        setTasks(prev => prev.map(task => 
          task.id === update.taskId 
            ? { ...task, ...update.data }
            : task
        ));
        break;
        
      case 'agentMessage':
        setChatMessages(prev => [...prev, update.message]);
        break;
        
      case 'taskCreated':
        setTasks(prev => [...prev, update.task]);
        break;
        
      case 'progress':
        setChatMessages(prev => [...prev, {
          id: Date.now().toString(),
          type: 'progress',
          content: update.content,
          metadata: { progress: update.progress },
          timestamp: new Date().toISOString(),
          agentName: update.agentName
        }]);
        break;
    }
  };

  const handleSendMessage = async () => {
    if (!chatInput.trim() || !selectedApp) return;

    const userMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: chatInput,
      timestamp: new Date().toISOString()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsTyping(true);

    try {
      const response = await ap3xClient.sendAgentMessage(selectedApp.id, chatInput);
      setChatMessages(prev => [...prev, response]);
      
      // Refresh tasks after message
      const taskData = await ap3xClient.getTaskUpdates(selectedApp.id);
      setTasks(taskData.tasks);
      
    } catch (err) {
      console.error('Error sending message:', err);
      setError(err instanceof Error ? err.message : 'Failed to send message');
    } finally {
      setIsTyping(false);
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getTaskIcon = (type: string) => {
    const icons = {
      feature: <Sparkles className="w-4 h-4" />,
      bugfix: <AlertCircle className="w-4 h-4" />,
      refactor: <Code className="w-4 h-4" />,
      documentation: <FileText className="w-4 h-4" />,
      optimization: <Zap className="w-4 h-4" />
    };
    return icons[type as keyof typeof icons] || <Zap className="w-4 h-4" />;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      completed: 'text-green-400',
      'in-progress': 'text-blue-400',
      blocked: 'text-red-400',
      pending: 'text-yellow-400'
    };
    return colors[status as keyof typeof colors] || 'text-gray-400';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      completed: <CheckCircle className="w-4 h-4" />,
      'in-progress': <Clock className="w-4 h-4" />,
      blocked: <AlertCircle className="w-4 h-4" />,
      pending: <Clock className="w-4 h-4" />
    };
    return icons[status as keyof typeof icons] || <Clock className="w-4 h-4" />;
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const groupedTasks = useMemo(() => {
    const grouped: Record<string, any[]> = {
      pending: [],
      'in-progress': [],
      completed: [],
      blocked: []
    };
    
    tasks.forEach(task => {
      grouped[task.status]?.push(task);
    });
    
    return grouped;
  }, [tasks]);

  if (!selectedApp) {
    return (
      <div className="h-full flex items-center justify-center text-[#666]">
        <div className="text-center">
          <Bot className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg mb-2">Welcome to AP3X AI Development</p>
          <p className="text-sm text-[#888]">Select an app to start collaborating with your AI development partner</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex items-center justify-center text-[#666]">
        <div className="text-center">
          <AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-500" />
          <p className="text-lg mb-2">Error Loading AP3X</p>
          <p className="text-sm text-[#888]">{error}</p>
        </div>
      </div>
    );
  }

  const personality = agentPersonalities[agentPersonality as keyof typeof agentPersonalities];

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-[#0a0a0a] to-[#111111] rounded-xl overflow-hidden border border-[#1a1a1a]">
      {/* Header */}
      <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between bg-[#0a0a0a]/50">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
            <div className="absolute inset-0 w-3 h-3 bg-purple-500 rounded-full animate-ping opacity-75"></div>
          </div>
          <div>
            <h2 className="font-medium text-white text-sm">
              {selectedApp.name} - {personality.name}
            </h2>
            <p className="text-xs text-[#666]">{personality.tone}</p>
          </div>
          {activeAgents.length > 0 && (
            <span className="text-xs bg-purple-900/20 text-purple-400 px-2 py-1 rounded">
              {activeAgents.length} agents active
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowTaskPanel(!showTaskPanel)}
            className="h-7 px-2 bg-[#1a1a1a] border border-[#333] text-[#888] hover:text-white rounded text-xs flex items-center gap-1"
          >
            <Target className="w-3 h-3" />
            Tasks
          </button>
          <button
            onClick={() => setShowAgentPanel(!showAgentPanel)}
            className="h-7 px-2 bg-[#1a1a1a] border border-[#333] text-[#888] hover:text-white rounded text-xs flex items-center gap-1"
          >
            <Users className="w-3 h-3" />
            Agents
          </button>
        </div>
      </div>

      <div className="flex-1 flex min-h-0">
        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto">
            <div className="px-6 py-4 space-y-4">
              {isLoading && (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Bot className="w-8 h-8 text-purple-400" />
                    </motion.div>
                    <p className="text-sm text-[#666] mt-2">Loading AP3X...</p>
                  </div>
                </div>
              )}
              
              {!isLoading && (
                <AnimatePresence>
                  {chatMessages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className="group"
                    >
                      <div className={`flex gap-3 ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                        <div className="flex-shrink-0">
                          <div
                            className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                              message.type === 'user'
                                ? 'bg-purple-600 text-white'
                                : message.type === 'agent'
                                ? 'bg-purple-900/50 text-purple-300 border border-purple-700/50'
                                : 'bg-green-900/50 text-green-300 border border-green-700/50'
                            }`}
                          >
                            {message.type === 'user' ? (
                              <User className="w-4 h-4" />
                            ) : (
                              <Bot className="w-4 h-4" />
                            )}
                          </div>
                        </div>
                        <div className={`flex-1 max-w-[85%] ${message.type === 'user' ? 'text-right' : ''}`}>
                          <motion.div
                            initial={{ scale: 0.9 }}
                            animate={{ scale: 1 }}
                            className={`inline-block px-4 py-3 rounded-lg text-sm leading-relaxed ${
                              message.type === 'user'
                                ? 'bg-purple-600 text-white'
                                : message.type === 'agent'
                                ? 'bg-purple-900/50 text-purple-200 border border-purple-700/50'
                                : 'bg-green-900/50 text-green-200 border border-green-700/50'
                            }`}
                          >
                            <div className="whitespace-pre-wrap">{message.content}</div>
                            {message.codeSnippet && (
                              <pre className="mt-2 bg-[#0a0a0a] border border-[#333] rounded p-2 text-xs overflow-x-auto">
                                <code>{message.codeSnippet}</code>
                              </pre>
                            )}
                          </motion.div>
                          <div className={`text-xs text-[#666] mt-1 ${message.type === 'user' ? 'text-right' : ''}`}>
                            {formatTime(message.timestamp)}
                            {message.agentName && (
                              <span className="ml-2 text-purple-400">@{message.agentName}</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
              )}

              {/* Agent Typing Indicator */}
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex gap-3"
                >
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium bg-purple-900/50 text-purple-300">
                      <Bot className="w-4 h-4" />
                    </div>
                  </div>
                  <div className="flex-1 max-w-[85%]">
                    <div className="inline-block px-4 py-3 rounded-lg text-sm bg-purple-900/30 text-purple-200">
                      <div className="flex items-center gap-2">
                        <span>AP3X is thinking</span>
                        <div className="flex gap-1">
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce"></div>
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Chat Input */}
          <div className="shrink-0 p-3 bg-[#0a0a0a] border-t border-[#1a1a1a]">
            <div className="relative bg-[#111111] rounded-xl">
              <textarea
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                placeholder={`Ask your AI partner to help you build something amazing...`}
                className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none"
                rows={3}
                style={{ minHeight: '60px', maxHeight: '120px' }}
              />
              <button
                onClick={handleSendMessage}
                disabled={!chatInput.trim() || isLoading}
                className="absolute right-2 top-3 w-8 h-8 bg-purple-600 hover:bg-purple-700 disabled:bg-[#333] rounded-lg flex items-center justify-center"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
            
            {/* Quick Actions */}
            <div className="flex items-center gap-2 mt-2">
              <button
                onClick={() => setShowTaskPanel(!showTaskPanel)}
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400 flex items-center gap-1"
              >
                <Target className="w-3 h-3" />
                Tasks
              </button>
              <button
                onClick={() => setShowAgentPanel(!showAgentPanel)}
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400 flex items-center gap-1"
              >
                <Users className="w-3 h-3" />
                Agents
              </button>
            </div>
          </div>
        </div>

        {/* Task Sidebar */}
        <AnimatePresence>
          {showTaskPanel && (
            <motion.div
              initial={{ x: 320 }}
              animate={{ x: 0 }}
              exit={{ x: 320 }}
              className="w-80 border-l border-[#1a1a1a] bg-[#0a0a0a]"
            >
              <div className="p-4 border-b border-[#1a1a1a]">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-white flex items-center gap-2">
                    <Target className="w-4 h-4 text-purple-400" />
                    AP3X Tasks
                  </h3>
                  <button
                    onClick={() => setShowTaskPanel(false)}
                    className="h-6 w-6 p-0 text-[#666] hover:text-white"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
                <div className="mt-2">
                  <div className="w-full bg-[#1a1a1a] rounded-full h-1">
                    <motion.div 
                      className="bg-purple-500 h-1 rounded-full" 
                      initial={{ width: 0 }}
                      animate={{ width: `${tasks.filter(t => t.status === 'completed').length / tasks.length * 100 || 0}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>
                  <p className="text-xs text-[#666] mt-1">
                    {tasks.filter(t => t.status === 'completed').length} / {tasks.length} completed
                  </p>
                </div>
              </div>
              
              <div className="h-[calc(100%-120px)] overflow-y-auto">
                <div className="p-4 space-y-4">
                  {tasks.length === 0 ? (
                    <div className="text-center text-[#666] py-8">
                      <motion.div
                        animate={{ rotate: [0, 360] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      </motion.div>
                      <p className="text-sm">No tasks yet</p>
                      <p className="text-xs mt-1">Ask AP3X to start planning</p>
                    </div>
                  ) : (
                    <>
                      {Object.entries(
                        tasks.reduce((acc, task) => {
                          acc[task.status] = acc[task.status] || [];
                          acc[task.status].push(task);
                          return acc;
                        }, {} as Record<string, any[]>)
                      ).map(([status, statusTasks]) => (
                        statusTasks.length > 0 && (
                          <div key={status}>
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="text-xs font-medium text-white uppercase tracking-wider">
                                {status.replace('-', ' ')}
                              </h4>
                              <span className="text-xs text-[#666]">({statusTasks.length})</span>
                            </div>
                            <div className="space-y-2">
                              {statusTasks.map((task) => (
                                <motion.div
                                  key={task.id}
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-3 hover:border-[#333]"
                                >
                                  <div className="flex items-start gap-2">
                                    <div className="mt-0.5">
                                      {getTaskIcon(task.type)}
                                    </div>
                                    <div className="flex-1">
                                      <h4 className="text-sm font-medium text-white">{task.title}</h4>
                                      <p className="text-xs text-[#666] mt-1">{task.description}</p>
                                      
                                      <div className="mt-2">
                                        <div className="flex items-center justify-between text-xs">
                                          <span className={getStatusColor(task.status)}>
                                            {getStatusIcon(task.status)} {task.status}
                                          </span>
                                          <span className="text-[#666]">{task.estimatedHours}h</span>
                                        </div>
                                        
                                        {task.status === 'in-progress' && (
                                          <div className="mt-2">
                                            <div className="w-full bg-[#1a1a1a] rounded-full h-1">
                                              <div 
                                                className="bg-blue-500 h-1 rounded-full" 
                                                style={{ width: `${task.progress}%` }}
                                              ></div>
                                            </div>
                                            <p className="text-xs text-[#666] mt-1">
                                              {task.progress}% complete
                                            </p>
                                          </div>
                                        )}
                                        
                                        {task.agentUpdates && task.agentUpdates.length > 0 && (
                                          <div className="mt-2">
                                            <p className="text-xs text-purple-400">
                                              💡 {task.agentUpdates[task.agentUpdates.length - 1]}
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </motion.div>
                              ))}
                            </div>
                          </div>
                        )
                      ))}
                    </>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Agent Panel */}
        <AnimatePresence>
          {showAgentPanel && (
            <motion.div
              initial={{ x: 320 }}
              animate={{ x: 0 }}
              exit={{ x: 320 }}
              className="w-64 border-l border-[#1a1a1a] bg-[#0a0a0a]"
            >
              <div className="p-4 border-b border-[#1a1a1a]">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-white flex items-center gap-2">
                    <Users className="w-4 h-4 text-purple-400" />
                    Active Agents
                  </h3>
                  <button
                    onClick={() => setShowAgentPanel(false)}
                    className="h-6 w-6 p-0 text-[#666] hover:text-white"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              </div>
              
              <div className="h-[calc(100%-60px)] overflow-y-auto">
                <div className="p-4 space-y-3">
                  {activeAgents.map((agent) => (
                    <motion.div
                      key={agent.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-3"
                    >
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <div>
                          <h4 className="text-sm font-medium text-white">{agent.name}</h4>
                          <p className="text-xs text-[#666]">{agent.type}</p>
                        </div>
                      </div>
                      <div className="mt-2">
                        <p className="text-xs text-[#888]">{agent.personality}</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {agent.capabilities?.slice(0, 3).map((capability: string) => (
                            <span key={capability} className="text-xs bg-[#1a1a1a] text-[#666] px-1 py-0.5 rounded">
                              {capability}
                            </span>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
