"use client"

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Bot, User, Plus, CheckCircle, Clock, AlertCircle, Sparkles, Code, FileText, Zap, Target, Users, Send, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface AP3XChatFinalProps {
  selectedApp?: any;
}

interface AP3XTask {
  id: string;
  title: string;
  description: string;
  type: string;
  status: 'pending' | 'in-progress' | 'completed' | 'blocked';
  progress: number;
  estimatedHours: number;
  agentUpdates: string[];
}

interface AgentMessage {
  id: string;
  type: 'user' | 'agent' | 'task-update';
  content: string;
  metadata?: any;
  timestamp: string;
  agentName?: string;
}

// Agent personality configurations
const agentPersonalities = {
  collaborative: {
    name: 'Collaborative Partner',
    tone: 'friendly and supportive',
    greeting: "Hey there! I'm your development partner. Let's build something amazing together! 🤝"
  },
  expert: {
    name: 'Expert Consultant',
    tone: 'professional and precise',
    greeting: "Greetings! I'm here to provide expert guidance and ensure we build with best practices. 🎯"
  },
  creative: {
    name: 'Creative Collaborator',
    tone: 'innovative and enthusiastic',
    greeting: "What's up! Ready to create something innovative? Let's think outside the box! ✨"
  },
  mentor: {
    name: 'Mentor Guide',
    tone: 'patient and educational',
    greeting: "Hello! I'll guide you through each step and help you learn along the way. 📚"
  }
};

export default function AP3XChatFinal({ selectedApp }: AP3XChatFinalProps) {
  const [chatMessages, setChatMessages] = useState<AgentMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [tasks, setTasks] = useState<AP3XTask[]>([]);
  const [activeAgents, setActiveAgents] = useState<any[]>([]);
  const [agentPersonality, setAgentPersonality] = useState('collaborative');
  const [showTaskPanel, setShowTaskPanel] = useState(true);
  const [showAgentPanel, setShowAgentPanel] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages]);

  // Load initial data
  useEffect(() => {
    if (selectedApp) {
      loadInitialData();
    }
  }, [selectedApp]);

  const loadInitialData = async () => {
    // Simulate loading tasks
    const mockTasks: AP3XTask[] = [
      {
        id: '1',
        title: 'Initialize Project Structure',
        description: 'Set up the basic project scaffolding with TypeScript and React',
        type: 'feature',
        status: 'completed',
        progress: 100,
        estimatedHours: 2,
        agentUpdates: ['Project structure created successfully', 'Dependencies installed']
      },
      {
        id: '2',
        title: 'Implement Authentication',
        description: 'Add user authentication with JWT tokens',
        type: 'feature',
        status: 'in-progress',
        progress: 65,
        estimatedHours: 4,
        agentUpdates: ['Auth service implemented', 'Working on UI components']
      },
      {
        id: '3',
        title: 'Setup Database Schema',
        description: 'Create database tables for users and projects',
        type: 'feature',
        status: 'pending',
        progress: 0,
        estimatedHours: 3,
        agentUpdates: ['Schema design complete']
      }
    ];
    
    setTasks(mockTasks);
    
    // Simulate active agents
    const mockAgents = [
      {
        id: '1',
        name: 'AP3X Planner',
        type: 'Planning Agent',
        personality: 'Strategic and organized',
        capabilities: ['Project Planning', 'Architecture Design', 'Code Review']
      },
      {
        id: '2',
        name: 'AP3X Coder',
        type: 'Coding Agent',
        personality: 'Efficient and precise',
        capabilities: ['Frontend', 'Backend', 'Testing']
      }
    ];
    
    setActiveAgents(mockAgents);
    
    // Initialize with greeting
    const personality = agentPersonalities[agentPersonality as keyof typeof agentPersonalities];
    const welcomeMessage: AgentMessage = {
      id: Date.now().toString(),
      type: 'agent',
      content: personality.greeting,
      timestamp: new Date().toISOString(),
      agentName: personality.name
    };
    
    setChatMessages([welcomeMessage]);
  };

  const handleSendMessage = async () => {
    if (!chatInput.trim() || !selectedApp) return;

    const userMessage: AgentMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: chatInput,
      timestamp: new Date().toISOString()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsTyping(true);

    // Simulate agent response
    setTimeout(() => {
      setIsTyping(false);
      
      const responses = [
        "Great question! Let me analyze the current state and provide you with a comprehensive solution.",
        "I understand what you're looking for. Based on the current codebase, here's my recommendation...",
        "Excellent idea! I'll start implementing this feature right away. Let me break it down into manageable tasks.",
        "I see the challenge here. Let me suggest an optimized approach that follows best practices.",
        "Perfect! This aligns well with our current architecture. I'll update the task list accordingly."
      ];
      
      const agentResponse: AgentMessage = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: responses[Math.floor(Math.random() * responses.length)],
        timestamp: new Date().toISOString(),
        agentName: 'AP3X Agent'
      };
      
      setChatMessages(prev => [...prev, agentResponse]);
      
      // Simulate task update
      if (Math.random() > 0.5) {
        const taskUpdate: AgentMessage = {
          id: (Date.now() + 2).toString(),
          type: 'task-update',
          content: `I've created a new task: "${chatInput.substring(0, 30)}..."`,
          metadata: { taskId: Date.now().toString(), progress: 0 },
          timestamp: new Date().toISOString(),
          agentName: 'AP3X Planner'
        };
        
        setChatMessages(prev => [...prev, taskUpdate]);
        
        // Add new task
        const newTask: AP3XTask = {
          id: Date.now().toString(),
          title: chatInput.substring(0, 50) + '...',
          description: chatInput,
          type: 'feature',
          status: 'pending',
          progress: 0,
          estimatedHours: 2,
          agentUpdates: ['Task created from user request']
        };
        
        setTasks(prev => [...prev, newTask]);
      }
    }, 1500);
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getTaskIcon = (type: string) => {
    const icons = {
      feature: <Sparkles className="w-4 h-4" />,
      bugfix: <AlertCircle className="w-4 h-4" />,
      refactor: <Code className="w-4 h-4" />,
      documentation: <FileText className="w-4 h-4" />,
      optimization: <Zap className="w-4 h-4" />
    };
    return icons[type as keyof typeof icons] || <Zap className="w-4 h-4" />;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      completed: 'text-green-400',
      'in-progress': 'text-blue-400',
      blocked: 'text-red-400',
      pending: 'text-yellow-400'
    };
    return colors[status as keyof typeof colors] || 'text-gray-400';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      completed: <CheckCircle className="w-4 h-4" />,
      'in-progress': <Clock className="w-4 h-4" />,
      blocked: <AlertCircle className="w-4 h-4" />,
      pending: <Clock className="w-4 h-4" />
    };
    return icons[status as keyof typeof icons] || <Clock className="w-4 h-4" />;
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const groupedTasks = useMemo(() => {
    const grouped: Record<string, AP3XTask[]> = {
      pending: [],
      'in-progress': [],
      completed: [],
      blocked: []
    };
    
    tasks.forEach(task => {
      grouped[task.status]?.push(task);
    });
    
    return grouped;
  }, [tasks]);

  if (!selectedApp) {
    return (
      <div className="h-full flex items-center justify-center text-[#666]">
        <div className="text-center">
          <Bot className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg mb-2">Welcome to AP3X AI Development</p>
          <p className="text-sm text-[#888]">Select an app to start collaborating with your AI development partner</p>
        </div>
      </div>
    );
  }

  const personality = agentPersonalities[agentPersonality as keyof typeof agentPersonalities];

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-[#0a0a0a] to-[#111111] rounded-xl overflow-hidden border border-[#1a1a1a]">
      {/* Header */}
      <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between bg-[#0a0a0a]/50">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
            <div className="absolute inset-0 w-3 h-3 bg-purple-500 rounded-full animate-ping opacity-75"></div>
          </div>
          <div>
            <h2 className="font-medium text-white text-sm">
              {selectedApp.name} - {personality.name}
            </h2>
            <p className="text-xs text-[#666]">{personality.tone}</p>
          </div>
          {activeAgents.length > 0 && (
            <span className="text-xs bg-purple-900/20 text-purple-400 px-2 py-1 rounded">
              {activeAgents.length} agents active
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={handleCreateChat}
            className="h-7 px-2 bg-[#1a1a1a] border border-[#333] text-[#888] hover:text-white rounded text-xs flex items-center gap-1"
          >
            <Plus className="w-3 h-3" />
            New Session
          </button>
        </div>
      </div>

      <div className="flex-1 flex min-h-0">
        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto">
            <div className="px-6 py-4 space-y-4">
              <AnimatePresence>
                {chatMessages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="group"
                  >
                    <div className={`flex gap-3 ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                      <div className="flex-shrink-0">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                            message.type === 'user'
                              ? 'bg-purple-600 text-white'
                              : message.type === 'agent'
                              ? 'bg-purple-900/50 text-purple-300 border border-purple-700/50'
                              : 'bg-green-900/50 text-green-300 border border-green-700/50'
                          }`}
                        >
                          {message.type === 'user' ? (
                            <User className="w-4 h-4" />
                          ) : (
                            <Bot className="w-4 h-4" />
                          )}
                        </div>
                      </div>
                      <div className={`flex-1 max-w-[85%] ${message.type === 'user' ? 'text-right' : ''}`}>
                        <motion.div
                          initial={{ scale: 0.9 }}
                          animate={{ scale: 1 }}
                          className={`inline-block px-4 py-3 rounded-lg text-sm leading-relaxed ${
                            message.type === 'user'
                              ? 'bg-purple-600 text-white'
                              : message.type === 'agent'
                              ? 'bg-purple-900/50 text-purple-200 border border-purple-700/50'
                              : 'bg-green-900/50 text-green-200 border border-green-700/50'
                          }`}
                        >
                          <div className="whitespace-pre-wrap">{message.content}</div>
                        </motion.div>
                        <div className={`text-xs text-[#666] mt-1 ${message.type === 'user' ? 'text-right' : ''}`}>
                          {formatTime(message.timestamp)}
                          {message.agentName && (
                            <span className="ml-2 text-purple-400">@{message.agentName}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Agent Typing Indicator */}
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex gap-3"
                >
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium bg-purple-900/50 text-purple-300">
                      <Bot className="w-4 h-4" />
                    </div>
                  </div>
                  <div className="flex-1 max-w-[85%]">
                    <div className="inline-block px-4 py-3 rounded-lg text-sm bg-purple-900/30 text-purple-200">
                      <div className="flex items-center gap-2">
                        <span>AP3X is thinking</span>
                        <div className="flex gap-1">
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce"></div>
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Chat Input */}
          <div className="shrink-0 p-3 bg-[#0a0a0a] border-t border-[#1a1a1a]">
            <div className="relative bg-[#111111] rounded-xl">
              <textarea
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={`Ask ${personality.name} to help you build something amazing...`}
                className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none"
                rows={3}
                style={{ minHeight: '60px', maxHeight: '120px' }}
              />
              <button
                onClick={handleSendMessage}
                disabled={!chatInput.trim()}
                className="absolute right-2 top-3 w-8 h-8 bg-purple-600 hover:bg-purple-700 disabled:bg-[#333] rounded-lg flex items-center justify-center"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
            
            {/* Quick Actions */}
            <div className="flex items-center gap-2 mt-2">
              <button
                onClick={() => setShowTaskPanel(!showTaskPanel)}
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400 flex items-center gap-1"
              >
                <Target className="w-3 h-3" />
                Tasks
              </button>
              <button
                onClick={() => setShowAgentPanel(!showAgentPanel)}
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400 flex items-center gap-1"
              >
                <Users className="w-3 h-3" />
                Agents
              </button>
            </div>
          </div>
        </div>

        {/* Task Sidebar */}
        <AnimatePresence>
          {showTaskPanel && (
            <motion.div
              initial={{ x: 320 }}
              animate={{ x: 0 }}
              exit={{ x: 320 }}
              className="w-80 border-l border-[#1a1a1a] bg-[#0a0a0a]"
            >
              <div className="p-4 border-b border-[#1a1a1a]">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-white flex items-center gap-2">
                    <Target className="w-4 h-4 text-purple-400" />
                    AP3X Tasks
                  </h3>
                  <button
                    onClick={() => setShowTaskPanel(false)}
                    className="h-6 w-6 p-0 text-[#666] hover:text-white"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
                <div className="mt-2">
                  <div className="w-full bg-[#1a1a1a] rounded-full h-1">
                    <div 
                      className="bg-purple-500 h-1 rounded-full" 
                      style={{ width: `${tasks.filter(t => t.status === 'completed').length / tasks.length * 100 || 0}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-[#666] mt-1">
                    {tasks.filter(t => t.status === 'completed').length} / {tasks.length} completed
                  </p>
                </div>
              </div>
              
              <div className="h-[calc(100%-120px)] overflow-y-auto">
                <div className="p-4 space-y-4">
                  {tasks.length === 0 ? (
                    <div className="text-center text-[#666] py-8">
                      <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No tasks yet</p>
                      <p className="text-xs mt-1">Ask AP3X to start planning</p>
                    </div>
                  ) : (
                    <>
                      {Object.entries(groupedTasks).map(([status, statusTasks]) => (
                        statusTasks.length > 0 && (
                          <div key={status}>
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="text-xs font-medium text-white uppercase tracking-wider">
                                {status.replace('-', ' ')}
                              </h4>
                              <span className="text-xs text-[#666]">({statusTasks.length})</span>
                            </div>
                            <div className="space-y-2">
                              {statusTasks.map((task) => (
                                <motion.div
                                  key={task.id}
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-3 hover:border-[#333]"
                                >
                                  <div className="flex items-start gap-2">
                                    <div className="mt-0.5">
                                      {getTaskIcon(task.type)}
                                    </div>
                                    <div className="flex-1">
                                      <h4 className="text-sm font-medium text-white">{task.title}</h4>
                                      <p className="text-xs text-[#666] mt-1">{task.description}</p>
                                      
                                      <div className="mt-2">
                                        <div className="flex items-center justify-between text-xs">
                                          <span className={getStatusColor(task.status)}>
                                            {getStatusIcon(task.status)} {task.status}
                                          </span>
                                          <span className="text-[#666]">{task.estimatedHours}h</span>
                                        </div>
                                        
                                        {task.status === 'in-progress' && (
                                          <div className="mt-2">
                                            <div className="w-full bg-[#1a1a1a] rounded-full h-1">
                                              <div 
                                                className="bg-blue-500 h-1 rounded-full" 
                                                style={{ width: `${task.progress}%` }}
                                              ></div>
                                            </div>
                                            <p className="text-xs text-[#666] mt-1">
                                              {task.progress}% complete
                                            </p>
                                          </div>
                                        )}
                                        
                                        {task.agentUpdates.length > 0 && (
                                          <div className="mt-2">
                                            <p className="text-xs text-purple-400">
                                              💡 {task.agentUpdates[task.agentUpdates.length - 1]}
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </motion.div>
                              ))}
                            </div>
                          </div>
                        )
                      ))}
                    </>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Agent Panel */}
        <AnimatePresence>
          {showAgentPanel && (
            <motion.div
              initial={{ x: 320 }}
              animate={{ x: 0 }}
              exit={{ x: 320 }}
              className="w-64 border-l border-[#1a1a1a] bg-[#0a0a0a]"
            >
              <div className="p-4 border-b border-[#1a1a1a]">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-white flex items-center gap-2">
                    <Users className="w-4 h-4 text-purple-400" />
                    Active Agents
                  </h3>
                  <button
                    onClick={() => setShowAgentPanel(false)}
                    className="h-6 w-6 p-0 text-[#666] hover:text-white"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              </div>
              
              <div className="h-[calc(100%-60px)] overflow-y-auto">
                <div className="p-4 space-y-3">
                  {activeAgents.map((agent) => (
                    <motion.div
                      key={agent.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-3"
                    >
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <div>
                          <h4 className="text-sm font-medium text-white">{agent.name}</h4>
                          <p className="text-xs text-[#666]">{agent.type}</p>
                        </div>
                      </div>
                      <div className="mt-2">
                        <p className="text-xs text-[#888]">{agent.personality}</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {agent.capabilities?.slice(0, 3).map((capability: string) => (
                            <span key={capability} className="text-xs bg-[#1a1a1a] text-[#666] px-1 py-0.5 rounded">
                              {capability}
                            </span>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
