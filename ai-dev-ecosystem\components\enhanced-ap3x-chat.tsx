"use client"

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useDyadChats, useDyadModels, useDyadStreaming } from '@/hooks/use-dyad';
import { ap3xClient, AP3XTask, AgentMessage, AP3XAgent } from '@/lib/ap3x-client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowUp, 
  Bot, 
  User, 
  Loader2, 
  MessageSquare,
  Settings,
  Plus,
  Trash2,
  CheckCircle,
  Clock,
  AlertCircle,
  Sparkles,
  Code,
  FileText,
  Zap,
  Brain,
  Target,
  Activity,
  Eye,
  Wand2,
  Lightbulb,
  TrendingUp,
  Users,
  GitBranch,
  FileCode,
  TestTube2,
  BookOpen,
  Settings2,
  Play,
  Pause,
  RotateCcw,
  Check,
  X,
  ChevronRight,
  ChevronDown,
  RefreshCw,
  Send,
  Mic,
  Image,
  Paperclip,
  Smile,
  Hash,
  Star,
  Heart,
  ThumbsUp,
  MessageCircle,
  Clock3,
  Calendar,
  Filter,
  Search,
  MoreVertical,
  ExternalLink,
  Copy,
  Download,
  Upload,
  Share2,
  Bookmark,
  Flag,
  AlertTriangle,
  Info,
  HelpCircle,
  Terminal,
  ActivitySquare,
  BarChart3,
  PieChart,
  LineChart,
  AreaChart,
  TrendingDown,
  Users2,
  UserCheck,
  UserPlus,
  UserMinus,
  UserX,
  Settings3,
  Sliders,
  Palette,
  Type,
  ImageIcon,
  Video,
  Music,
  File,
  Folder,
  FolderOpen,
  Archive,
  Save,
  Edit3,
  Trash,
  EyeOff,
  EyeOn,
  Lock,
  Unlock,
  Key,
  Shield,
  ShieldCheck,
  ShieldAlert,
  ShieldOff,
  Security,
  Verified,
  AlertOctagon,
  AlertTriangle,
  InfoIcon,
  Help,
  Question,
  SearchIcon,
  FilterIcon,
  SortAsc,
  SortDesc,
  ArrowDown,
  ArrowUp,
  ArrowLeft,
  ArrowRight,
  ChevronsUpDown,
  MoreHorizontal,
  Menu,
  XCircle,
  CheckCircle2,
  AlertCircle2,
  InfoCircle,
  HelpCircle2,
  Settings4,
  Sliders2,
  Palette2,
  Type2,
  Image2,
  Video2,
  Music2,
  File2,
  Folder2,
  Archive2,
  Save2,
  Edit4,
  Trash3,
  Eye2,
  EyeOff2,
  Lock2,
  Unlock2,
  Key2,
  Shield2,
  ShieldCheck2,
  ShieldAlert2,
  ShieldOff2,
  Security2,
  Verified2,
  AlertOctagon2,
  AlertTriangle2,
  Info2,
  Help2,
  Question2,
  Search2,
  Filter2,
  SortAsc2,
  SortDesc2,
  ArrowDown2,
  ArrowUp2,
  ArrowLeft2,
  ArrowRight2,
  ChevronsUpDown2,
  MoreHorizontal2,
  Menu2,
  XCircle2,
  CheckCircle3,
  AlertCircle3,
  InfoCircle2,
  HelpCircle3
} from 'lucide-react';
import { App, Chat, ChatMessage } from '@/lib/dyad-client';
import { useSocket } from '@/hooks/use-socket';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import { useAtom } from 'jotai';
import { atom } from 'jotai';

// Real-time state atoms
const agentStatusAtom = atom<string>('ready');
const activeAgentsAtom = atom<AP3XAgent[]>([]);
const currentTaskAtom = atom<AP3XTask | null>(null);
const agentPersonalityAtom = atom<string>('collaborative');

interface EnhancedAP3XChatProps {
  selectedApp?: App;
}

// Agent personality configurations
const agentPersonalities = {
  collaborative: {
    name: 'Collaborative Partner',
    tone: 'friendly and supportive',
    greeting: "Hey there! I'm your development partner. Let's build something amazing together!",
    emoji: '🤝'
  },
  expert: {
    name: 'Expert Consultant',
    tone: 'professional and precise',
    greeting: "Greetings! I'm here to provide expert guidance and ensure we build with best practices.",
    emoji: '🎯'
  },
  creative: {
    name: 'Creative Collaborator',
    tone: 'innovative and enthusiastic',
    greeting: "What's up! Ready to create something innovative? Let's think outside the box!",
    emoji: '✨'
  },
  mentor: {
    name: 'Mentor Guide',
    tone: 'patient and educational',
    greeting: "Hello! I'll guide you through each step and help you learn along the way.",
    emoji: '📚'
  }
};

export default function EnhancedAP3XChat({ selectedApp }: EnhancedAP3XChatProps) {
  const { chats, createChat, deleteChat, addMessage } = useDyadChats(selectedApp?.id);
  const { models } = useDyadModels();
  const { isStreaming, streamContent, startStream, resetStream } = useDyadStreaming();
  
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [chatMessages, setChatMessages] = useState<AgentMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [selectedModel, setSelectedModel] = useState('moonshotai/kimi-k2');
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [tasks, setTasks] = useState<AP3XTask[]>([]);
  const [activeAgents, setActiveAgents] = useState<AP3XAgent[]>([]);
  const [agentStatus, setAgentStatus] = useState<string>('ready');
  const [agentPersonality, setAgentPersonality] = useAtom(agentPersonalityAtom);
  const [currentTask, setCurrentTask] = useAtom(currentTaskAtom);
  const [showTaskPanel, setShowTaskPanel] = useState(true);
  const [showAgentPanel, setShowAgentPanel] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [conversationContext, setConversationContext] = useState<Record<string, any>>({});
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  // Enhanced real-time updates via Socket.IO
  useSocket({
    onTaskUpdated: (update) => {
      if (update.appId === selectedApp?.id) {
        setTasks(prev => prev.map(task => 
          task.id === update.taskId ? { ...task, ...update } : task
        ));
        
        // Add contextual agent message about task update
        const taskMessage: AgentMessage = {
          id: Date.now().toString(),
          type: 'task-update',
          content: generateTaskUpdateMessage(update),
          metadata: {
            taskId: update.taskId,
            progress: update.progress,
            status: update.status,
            agentName: update.agentName
          },
          timestamp: new Date().toISOString(),
          agentName: update.agentName || 'AP3X Agent'
        };
        
        setChatMessages(prev => [...prev, taskMessage]);
      }
    },
    onAgentMessage: (message) => {
      if (message.appId === selectedApp?.id) {
        setChatMessages(prev => [...prev, message]);
      }
    },
    onAgentStatus: (status) => {
      setAgentStatus(status.status);
      setActiveAgents(status.agents || []);
    }
  });

  // Generate contextual task update messages
  const generateTaskUpdateMessage = (update: any) => {
    const personality = agentPersonalities[agentPersonality as keyof typeof agentPersonalities];
    const messages = {
      completed: [
        `Excellent! I've successfully completed "${update.title}". The implementation looks solid and follows our best practices.`,
        `Great news! "${update.title}" is now complete. Ready to move on to the next phase.`,
        `Mission accomplished! "${update.title}" has been implemented with comprehensive testing and documentation.`
      ],
      'in-progress': [
        `Making great progress on "${update.title}" - currently at ${update.progress}% completion.`,
        `Working through "${update.title}" step by step. We're ${update.progress}% of the way there!`,
        `Continuing development on "${update.title}". Progress update: ${update.progress}% complete.`
      ],
      blocked: [
        `I've hit a roadblock with "${update.title}". Let me analyze the issue and suggest some solutions.`,
        `Encountered some challenges with "${update.title}". Here's what I'm thinking...`,
        `"${update.title}" needs some attention. There are a few blocking issues to resolve.`
      ]
    };

    const statusMessages = messages[update.status as keyof typeof messages] || [`Update on "${update.title}": ${update.status}`];
    return statusMessages[Math.floor(Math.random() * statusMessages.length)];
  };

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages, streamContent]);

  // Load agent conversation and tasks
  useEffect(() => {
    if (selectedApp) {
      loadAgentConversation();
      loadTasks();
      loadAgents();
      subscribeToAgentUpdates();
    }
  }, [selectedApp]);

  const loadAgentConversation = async () => {
    if (!selectedApp) return;
    try {
      const conversation = await ap3xClient.getAgentConversation(selectedApp.id);
      setChatMessages(conversation);
    } catch (error) {
      console.error('Failed to load agent conversation:', error);
    }
  };

  const loadTasks = async () => {
    if (!selectedApp) return;
    try {
      const { tasks } = await ap3xClient.getTaskUpdates(selectedApp.id);
      setTasks(tasks);
    } catch (error) {
      console.error('Failed to load tasks:', error);
    }
  };

  const loadAgents = async () => {
    try {
      const agents = await ap3xClient.getAgents();
      setActiveAgents(agents.filter(a => a.status === 'active'));
    } catch (error) {
      console.error('Failed to load agents:', error);
    }
  };

  const subscribeToAgentUpdates = () => {
    if (!selectedApp) return;
    
    ap3xClient.streamAgentUpdates(selectedApp.id, (update) => {
      if (update.type === 'task-update') {
        setTasks(prev => prev.map(task => 
          task.id === update.taskId ? { ...task, ...update } : task
        ));
      } else if (update.type === 'agent-message') {
        setChatMessages(prev => [...prev, update]);
      } else if (update.type === 'agent-status') {
        setAgentStatus(update.status);
      }
    });
  };

  const handleCreateChat = async () => {
    if (!selectedApp) return;
    
    try {
      const newChat = await createChat(selectedApp.id, `AP3X Session ${chats.length + 1}`);
      setSelectedChat(newChat);
      
      // Initialize AP3X agent with personality-based greeting
      const personality = agentPersonalities[agentPersonality as keyof typeof agentPersonalities];
      const welcomeMessage: AgentMessage = {
        id: Date.now().toString(),
        type: 'agent',
        content: `${personality.greeting} ${personality.emoji}`,
        metadata: {
          personality: agentPersonality,
          capabilities: activeAgents.flatMap(a => a.capabilities),
          currentProject: selectedApp.name
        },
        timestamp: new Date().toISOString(),
        agentName: personality.name
      };
      
      setChatMessages([welcomeMessage]);
    } catch (err) {
      console.error('Failed to create chat:', err);
    }
  };

  const handleSendMessage = async () => {
    if (!chatInput.trim() || !selectedApp) return;

    const userMessage: AgentMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: chatInput,
      metadata: {
        context: conversationContext,
        timestamp: Date.now()
      },
      timestamp: new Date().toISOString()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsTyping(true);

    try {
      // Simulate agent thinking
      setTimeout(() => setIsTyping(false), 1000);

      // Send to AP3X agent with context
      const agentResponse = await ap3xClient.sendAgentMessage(
        selectedApp.id, 
        chatInput,
        agentPersonality
      );
      
      setChatMessages(prev => [...prev, agentResponse]);
      
      // Update tasks if agent created new ones
      const { tasks: updatedTasks } = await ap3xClient.getTaskUpdates(selectedApp.id);
      setTasks(updatedTasks);
      
    } catch (err) {
      console.error('Failed to send message:', err);
      setIsTyping(false);
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getTaskIcon = (type: string) => {
    const icons = {
      feature: <Sparkles className="w-4 h-4" />,
      bugfix: <AlertCircle className="w-4 h-4" />,
      refactor: <Code className="w-4 h-4" />,
      documentation: <FileText className="w-4 h-4" />,
      optimization: <Zap className="w-4 h-4" />
    };
    return icons[type as keyof typeof icons] || <Zap className="w-4 h-4" />;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      completed: 'text-green-400',
      'in-progress': 'text-blue-400',
      blocked: 'text-red-400',
      pending: 'text-yellow-400'
    };
    return colors[status as keyof typeof colors] || 'text-gray-400';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      completed: <CheckCircle className="w-4 h-4" />,
      'in-progress': <Clock className="w-4 h-4" />,
      blocked: <AlertCircle className="w-4 h-4" />,
      pending: <Clock3 className="w-4 h-4" />
    };
    return icons[status as keyof typeof icons] || <Clock className="w-4 h-4" />;
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const groupedTasks = useMemo(() => {
    const grouped: Record<string, AP3XTask[]> = {
      pending: [],
      'in-progress': [],
      completed: [],
      blocked: []
    };
    
    tasks.forEach(task => {
      grouped[task.status]?.push(task);
    });
    
    return grouped;
  }, [tasks]);

  if (!selectedApp) {
    return (
      <div className="h-full flex items-center justify-center text-[#666]">
        <div className="text-center">
          <Bot className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg mb-2">Welcome to AP3X AI Development</p>
          <p className="text-sm text-[#888]">Select an app to start collaborating with your AI development partner</p>
        </div>
      </div>
    );
  }

  const personality = agentPersonalities[agentPersonality as keyof typeof agentPersonalities];

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-[#0a0a0a] to-[#111111] rounded-xl overflow-hidden border border-[#1a1a1a]">
      {/* Header with Agent Status */}
      <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between bg-[#0a0a0a]/50 backdrop-blur-sm">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
            <div className="absolute inset-0 w-3 h-3 bg-purple-500 rounded-full animate-ping opacity-75"></div>
          </div>
          <div>
            <h2 className="font-medium text-white text-sm">
              {selectedApp.name} - {personality.name}
            </h2>
            <p className="text-xs text-[#666]">{personality.tone}</p>
          </div>
          {activeAgents.length > 0 && (
            <Badge variant="secondary" className="bg-purple-900/20 text-purple-400 border-purple-800">
              {activeAgents.length} agents active
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleCreateChat}
            className="h-7 px-2 bg-[#1a1a1a] border-[#333] text-[#888] hover:text-white hover:bg-[#2a2a2a]"
          >
            <Plus className="w-3 h-3 mr-1" />
            New Session
          </Button>
          
          <Select value={selectedModel} onValueChange={setSelectedModel}>
            <SelectTrigger className="w-48 h-7 bg-[#1a1a1a] border-[#333] text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-[#1a1a1a] border-[#333]">
              {models
                .filter(model => model.provider === 'openrouter')
                .slice(0, 10)
                .map((model) => (
                  <SelectItem key={model.name} value={model.name} className="text-xs">
                    {model.displayName || model.name}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex-1 flex min-h-0">
        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <ScrollArea className="flex-1">
            <div className="px-6 py-4 space-y-4">
              <AnimatePresence>
                {chatMessages.map((message, index) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                    className="group"
                  >
                    <div className={`flex gap-3 ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                      <div className="flex-shrink-0">
                        <div
                          className={cn(
                            "w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-all duration-200",
                            message.type === 'user'
                              ? 'bg-purple-600 text-white shadow-lg shadow-purple-500/20'
                              : message.type === 'agent'
                              ? 'bg-purple-900/50 text-purple-300 border border-purple-700/50 shadow-lg shadow-purple-500/10'
                              : message.type === 'task-update'
                              ? 'bg-green-900/50 text-green-300 border border-green-700/50 shadow-lg shadow-green-500/10'
                              : 'bg-[#1a1a1a] text-[#888] border border-[#2a2a2a]'
                          )}
                        >
                          {message.type === 'user' ? (
                            <User className="w-4 h-4" />
                          ) : message.type === 'agent' ? (
                            <Bot className="w-4 h-4" />
                          ) : message.type === 'task-update' ? (
                            <Sparkles className="w-4 h-4" />
                          ) : (
                            <Activity className="w-4 h-4" />
                          )}
                        </div>
                      </div>
                      <div className={`flex-1 max-w-[85%] ${message.type === 'user' ? 'text-right' : ''}`}>
                        <motion.div
                          initial={{ scale: 0.9 }}
                          animate={{ scale: 1 }}
                          transition={{ duration: 0.2 }}
                          className={cn(
                            "inline-block px-4 py-3 rounded-lg text-sm leading-relaxed shadow-lg",
                            message.type === 'user'
                              ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-purple-500/20'
                              : message.type === 'agent'
                              ? 'bg-gradient-to-r from-purple-900/50 to-purple-800/50 text-purple-200 border border-purple-700/50 shadow-purple-500/10'
                              : message.type === 'task-update'
                              ? 'bg-gradient-to-r from-green-900/50 to-green-800/50 text-green-200 border border-green-700/50 shadow-green-500/10'
                              : 'bg-gradient-to-r from-[#111111] to-[#1a1a1a] text-[#e5e5e5] shadow-md'
                          )}
                        >
                          <div className="whitespace-pre-wrap">{message.content}</div>
                          {message.metadata?.progress !== undefined && (
                            <div className="mt-2">
                              <Progress value={message.metadata.progress} className="h-1" />
                              <div className="flex justify-between text-xs mt-1">
                                <span>Progress</span>
                                <span>{message.metadata.progress}%</span>
                              </div>
                            </div>
                          )}
                          {message.metadata?.suggestions && (
                            <div className="mt-3 space-y-1">
                              <p className="text-xs text-purple-300">Suggestions:</p>
                              {message.metadata.suggestions.map((suggestion: string, i: number) => (
                                <div key={i} className="text-xs text-purple-400 ml-2">• {suggestion}</div>
                              ))}
                            </div>
                          )}
                        </motion.div>
                        <div className={cn("text-xs text-[#666] mt-1", message.type === 'user' ? 'text-right' : '')}>
                          {formatTime(message.timestamp)}
                          {message.agentName && (
                            <span className="ml-2 text-purple-400">@{message.agentName}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Agent Typing Indicator */}
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex gap-3"
                >
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium bg-purple-900/50 text-purple-300 border border-purple-700/50">
                      <Bot className="w-4 h-4" />
                    </div>
                  </div>
                  <div className="flex-1 max-w-[85%]">
                    <div className="inline-block px-4 py-3 rounded-lg text-sm leading-relaxed bg-purple-900/30 text-purple-200 border border-purple-700/50">
                      <div className="flex items-center gap-2">
                        <span>AP3X is thinking</span>
                        <div className="flex gap-1">
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Streaming Response */}
              {isStreaming && streamContent && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex gap-3"
                >
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium bg-purple-900/50 text-purple-300 border border-purple-700/50">
                      <Bot className="w-4 h-4" />
                    </div>
                  </div>
                  <div className="flex-1 max-w-[85%]">
                    <div className="inline-block px-4 py-3 rounded-lg text-sm leading-relaxed bg-purple-900/30 text-purple-200 border border-purple-700/50 shadow-md">
                      <div className="whitespace-pre-wrap">{streamContent}</div>
                      <div className="inline-block w-2 h-4 bg-purple-500 animate-pulse ml-1"></div>
                    </div>
                  </div>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Enhanced Chat Input */}
          <div className="shrink-0 p-3 bg-[#0a0a0a] shadow-inner border-t border-[#1a1a1a]">
            <div className="relative bg-[#111111] rounded-xl p-1 focus-within:shadow-purple-500/20 focus-within:shadow-lg transition-all shadow-md">
              <textarea
                ref={textareaRef}
                value={chatInput}
                onChange={(e) => {
                  setChatInput(e.target.value);
                  const textarea = e.target as HTMLTextAreaElement;
                  textarea.style.height = 'auto';
                  const scrollHeight = textarea.scrollHeight;
                  const maxHeight = 240;
                  textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';
                }}
                onKeyDown={handleKeyDown}
                placeholder={`Ask ${personality.name} to help you build something amazing...`}
                className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none leading-6 scrollbar-none overflow-y-auto"
                rows={3}
                style={{ minHeight: '84px', maxHeight: '240px' }}
                disabled={isStreaming}
              />
              <button
                onClick={handleSendMessage}
                disabled={!chatInput.trim() || isStreaming}
                className="absolute right-2 top-3 w-8 h-8 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 disabled:from-[#333] disabled:to-[#333] disabled:text-[#666] rounded-lg flex items-center justify-center transition-all shadow-md hover:shadow-lg"
              >
                {isStreaming ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
              </button>
            </div>
            
            {/* Quick Actions */}
            <div className="flex items-center gap-2 mt-2">
              <Button
                size="sm"
                variant="ghost"
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400"
                onClick={() => setShowTaskPanel(!showTaskPanel)}
              >
                <Target className="w-3 h-3 mr-1" />
                Tasks
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400"
                onClick={() => setShowAgentPanel(!showAgentPanel)}
              >
                <Users className="w-3 h-3 mr-1" />
                Agents
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400"
                onClick={() => {
                  const personalities = Object.keys(agentPersonalities);
                  const currentIndex = personalities.indexOf(agentPersonality);
                  const nextIndex = (currentIndex + 1) % personalities.length;
                  setAgentPersonality(personalities[nextIndex]);
                }}
              >
                <Brain className="w-3 h-3 mr-1" />
                {personality.name.split(' ')[0]}
              </Button>
            </div>
          </div>
        </div>

        {/* Enhanced Task Sidebar */}
        <AnimatePresence>
          {showTaskPanel && (
            <motion.div
              initial={{ x: 320 }}
              animate={{ x: 0 }}
              exit={{ x: 320 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              className="w-80 border-l border-[#1a1a1a] bg-[#0a0a0a]"
            >
              <div className="p-4 border-b border-[#1a1a1a]">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-white flex items-center gap-2">
                    <Target className="w-4 h-4 text-purple-400" />
                    AP3X Tasks
                  </h3>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                    onClick={() => setShowTaskPanel(false)}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
                <div className="mt-2">
                  <Progress 
                    value={tasks.filter(t => t.status === 'completed').length / tasks.length * 100 || 0} 
                    className="h-1" 
                  />
                  <p className="text-xs text-[#666] mt-1">
                    {tasks.filter(t => t.status === 'completed').length} / {tasks.length} completed
                  </p>
                </div>
              </div>
              
              <ScrollArea className="h-[calc(100%-120px)]">
                <div className="p-4 space-y-4">
                  {tasks.length === 0 ? (
                    <div className="text-center text-[#666] py-8">
                      <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No tasks yet</p>
                      <p className="text-xs mt-1">Ask AP3X to start planning</p>
                    </div>
                  ) : (
                    <>
                      {Object.entries(groupedTasks).map(([status, statusTasks]) => (
                        statusTasks.length > 0 && (
                          <div key={status}>
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="text-xs font-medium text-white uppercase tracking-wider">
                                {status.replace('-', ' ')}
                              </h4>
                              <span className="text-xs text-[#666]">({statusTasks.length})</span>
                            </div>
                            <div className="space-y-2">
                              {statusTasks.map((task) => (
                                <motion.div
                                  key={task.id}
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-3 hover:border-[#333] transition-colors"
                                >
                                  <div className="flex items-start gap-2">
                                    <div className="mt-0.5">
                                      {getTaskIcon(task.type)}
                                    </div>
                                    <div className="flex-1">
                                      <h4 className="text-sm font-medium text-white">{task.title}</h4>
                                      <p className="text-xs text-[#666] mt-1">{task.description}</p>
                                      
                                      <div className="mt-2">
                                        <div className="flex items-center justify-between text-xs">
                                          <span className={getStatusColor(task.status)}>
                                            {getStatusIcon(task.status)} {task.status}
                                          </span>
                                          <span className="text-[#666]">{task.estimatedHours}h</span>
                                        </div>
                                        
                                        {task.status === 'in-progress' && (
                                          <div className="mt-2">
                                            <Progress value={task.progress} className="h-1" />
                                            <p className="text-xs text-[#666] mt-1">
                                              {task.progress}% complete
                                            </p>
                                          </div>
                                        )}
                                        
                                        {task.agentUpdates.length > 0 && (
                                          <div className="mt-2">
                                            <p className="text-xs text-purple-400">
                                              💡 {task.agentUpdates[task.agentUpdates.length - 1]}
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </motion.div>
                              ))}
                            </div>
                          </div>
                        )
                      ))}
                    </>
                  )}
                </div>
              </ScrollArea>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Agent Panel */}
        <AnimatePresence>
          {showAgentPanel && (
            <motion.div
              initial={{ x: 320 }}
              animate={{ x: 0 }}
              exit={{ x: 320 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              className="w-64 border-l border-[#1a1a1a] bg-[#0a0a0a]"
            >
              <div className="p-4 border-b border-[#1a1a1a]">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-white flex items-center gap-2">
                    <Users className="w-4 h-4 text-purple-400" />
                    Active Agents
                  </h3>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                    onClick={() => setShowAgentPanel(false)}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              
              <ScrollArea className="h-[calc(100%-60px)]">
                <div className="p-4 space-y-3">
                  {activeAgents.map((agent) => (
                    <motion.div
                      key={agent.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-3"
                    >
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <div>
                          <h4 className="text-sm font-medium text-white">{agent.name}</h4>
                          <p className="text-xs text-[#666]">{agent.type}</p>
                        </div>
                      </div>
                      <div className="mt-2">
                        <p className="text-xs text-[#888]">{agent.personality}</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {agent.capabilities.slice(0, 3).map((capability) => (
                            <Badge key={capability} variant="outline" className="text-xs h-4 px-1">
                              {capability}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </ScrollArea>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
