"use client"

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useDyadChats, useDyadModels, useDyadStreaming } from '@/hooks/use-dyad';
import { ap3xClient } from '@/lib/ap3x-client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card } from '@/components/ui/card';
import { 
  ArrowUp, 
  Bot, 
  User, 
  Loader2, 
  Plus,
  CheckCircle,
  Clock,
  AlertCircle,
  Sparkles,
  Code,
  FileText,
  Zap,
  Brain,
  Target,
  Users,
  Send,
  X
} from 'lucide-react';
import { A<PERSON>, <PERSON><PERSON> } from '@/lib/dyad-client';
import { motion, AnimatePresence } from 'framer-motion';

interface AP3XChatOptimizedProps {
  selectedApp?: App;
}

interface AP3XTask {
  id: string;
  title: string;
  description: string;
  type: string;
  status: 'pending' | 'in-progress' | 'completed' | 'blocked';
  progress: number;
  estimatedHours: number;
  agentUpdates: string[];
}

interface AgentMessage {
  id: string;
  type: 'user' | 'agent' | 'task-update';
  content: string;
  metadata?: any;
  timestamp: string;
  agentName?: string;
}

// Agent personality configurations
const agentPersonalities = {
  collaborative: {
    name: 'Collaborative Partner',
    tone: 'friendly and supportive',
    greeting: "Hey there! I'm your development partner. Let's build something amazing together! 🤝"
  },
  expert: {
    name: 'Expert Consultant',
    tone: 'professional and precise',
    greeting: "Greetings! I'm here to provide expert guidance and ensure we build with best practices. 🎯"
  },
  creative: {
    name: 'Creative Collaborator',
    tone: 'innovative and enthusiastic',
    greeting: "What's up! Ready to create something innovative? Let's think outside the box! ✨"
  },
  mentor: {
    name: 'Mentor Guide',
    tone: 'patient and educational',
    greeting: "Hello! I'll guide you through each step and help you learn along the way. 📚"
  }
};

export default function AP3XChatOptimized({ selectedApp }: AP3XChatOptimizedProps) {
  const { chats, createChat, deleteChat } = useDyadChats(selectedApp?.id);
  const { models } = useDyadModels();
  const { isStreaming, streamContent } = useDyadStreaming();
  
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [chatMessages, setChatMessages] = useState<AgentMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [selectedModel, setSelectedModel] = useState('moonshotai/kimi-k2');
  const [tasks, setTasks] = useState<AP3XTask[]>([]);
  const [activeAgents, setActiveAgents] = useState<any[]>([]);
  const [agentPersonality, setAgentPersonality] = useState('collaborative');
  const [showTaskPanel, setShowTaskPanel] = useState(true);
  const [showAgentPanel, setShowAgentPanel] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages, streamContent]);

  // Load agent conversation and tasks
  useEffect(() => {
    if (selectedApp) {
      loadAgentConversation();
      loadTasks();
      loadAgents();
    }
  }, [selectedApp]);

  const loadAgentConversation = async () => {
    if (!selectedApp) return;
    try {
      const conversation = await ap3xClient.getAgentConversation(selectedApp.id);
      setChatMessages(conversation);
    } catch (error) {
      console.error('Failed to load agent conversation:', error);
    }
  };

  const loadTasks = async () => {
    if (!selectedApp) return;
    try {
      const { tasks } = await ap3xClient.getTaskUpdates(selectedApp.id);
      setTasks(tasks);
    } catch (error) {
      console.error('Failed to load tasks:', error);
    }
  };

  const loadAgents = async () => {
    try {
      const agents = await ap3xClient.getAgents();
      setActiveAgents(agents.filter((a: any) => a.status === 'active'));
    } catch (error) {
      console.error('Failed to load agents:', error);
    }
  };

  const handleCreateChat = async () => {
    if (!selectedApp) return;
    
    try {
      const newChat = await createChat(selectedApp.id, `AP3X Session ${chats.length + 1}`);
      setSelectedChat(newChat);
      
      // Initialize AP3X agent with personality-based greeting
      const personality = agentPersonalities[agentPersonality as keyof typeof agentPersonalities];
      const welcomeMessage: AgentMessage = {
        id: Date.now().toString(),
        type: 'agent',
        content: personality.greeting,
        timestamp: new Date().toISOString(),
        agentName: personality.name
      };
      
      setChatMessages([welcomeMessage]);
    } catch (err) {
      console.error('Failed to create chat:', err);
    }
  };

  const handleSendMessage = async () => {
    if (!chatInput.trim() || !selectedApp) return;

    const userMessage: AgentMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: chatInput,
      timestamp: new Date().toISOString()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsTyping(true);

    try {
      // Simulate agent thinking
      setTimeout(() => setIsTyping(false), 1500);

      // Send to AP3X agent
      const agentResponse = await ap3xClient.sendAgentMessage(
        selectedApp.id, 
        chatInput,
        agentPersonality
      );
      
      setChatMessages(prev => [...prev, agentResponse]);
      
      // Update tasks
      const { tasks: updatedTasks } = await ap3xClient.getTaskUpdates(selectedApp.id);
      setTasks(updatedTasks);
      
    } catch (err) {
      console.error('Failed to send message:', err);
      setIsTyping(false);
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getTaskIcon = (type: string) => {
    const icons = {
      feature: <Sparkles className="w-4 h-4" />,
      bugfix: <AlertCircle className="w-4 h-4" />,
      refactor: <Code className="w-4 h-4" />,
      documentation: <FileText className="w-4 h-4" />,
      optimization: <Zap className="w-4 h-4" />
    };
    return icons[type as keyof typeof icons] || <Zap className="w-4 h-4" />;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      completed: 'text-green-400',
      'in-progress': 'text-blue-400',
      blocked: 'text-red-400',
      pending: 'text-yellow-400'
    };
    return colors[status as keyof typeof colors] || 'text-gray-400';
  };

  const getStatusIcon = (status: string) => {
    const icons = {
      completed: <CheckCircle className="w-4 h-4" />,
      'in-progress': <Clock className="w-4 h-4" />,
      blocked: <AlertCircle className="w-4 h-4" />,
      pending: <Clock className="w-4 h-4" />
    };
    return icons[status as keyof typeof icons] || <Clock className="w-4 h-4" />;
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const groupedTasks = useMemo(() => {
    const grouped: Record<string, AP3XTask[]> = {
      pending: [],
      'in-progress': [],
      completed: [],
      blocked: []
    };
    
    tasks.forEach(task => {
      grouped[task.status]?.push(task);
    });
    
    return grouped;
  }, [tasks]);

  if (!selectedApp) {
    return (
      <div className="h-full flex items-center justify-center text-[#666]">
        <div className="text-center">
          <Bot className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg mb-2">Welcome to AP3X AI Development</p>
          <p className="text-sm text-[#888]">Select an app to start collaborating with your AI development partner</p>
        </div>
      </div>
    );
  }

  const personality = agentPersonalities[agentPersonality as keyof typeof agentPersonalities];

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-[#0a0a0a] to-[#111111] rounded-xl overflow-hidden border border-[#1a1a1a]">
      {/* Header */}
      <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between bg-[#0a0a0a]/50">
        <div className="flex items-center gap-3">
          <div className="relative">
            <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
            <div className="absolute inset-0 w-3 h-3 bg-purple-500 rounded-full animate-ping opacity-75"></div>
          </div>
          <div>
            <h2 className="font-medium text-white text-sm">
              {selectedApp.name} - {personality.name}
            </h2>
            <p className="text-xs text-[#666]">{personality.tone}</p>
          </div>
          {activeAgents.length > 0 && (
            <Badge variant="secondary" className="bg-purple-900/20 text-purple-400">
              {activeAgents.length} agents active
            </Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleCreateChat}
            className="h-7 px-2 bg-[#1a1a1a] border-[#333] text-[#888] hover:text-white"
          >
            <Plus className="w-3 h-3 mr-1" />
            New Session
          </Button>
          
          <Select value={selectedModel} onValueChange={setSelectedModel}>
            <SelectTrigger className="w-48 h-7 bg-[#1a1a1a] border-[#333] text-xs">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-[#1a1a1a] border-[#333]">
              {models
                .filter(model => model.provider === 'openrouter')
                .slice(0, 10)
                .map((model) => (
                  <SelectItem key={model.name} value={model.name} className="text-xs">
                    {model.displayName || model.name}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex-1 flex min-h-0">
        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <ScrollArea className="flex-1">
            <div className="px-6 py-4 space-y-4">
              <AnimatePresence>
                {chatMessages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="group"
                  >
                    <div className={`flex gap-3 ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                      <div className="flex-shrink-0">
                        <div
                          className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                            message.type === 'user'
                              ? 'bg-purple-600 text-white'
                              : message.type === 'agent'
                              ? 'bg-purple-900/50 text-purple-300 border border-purple-700/50'
                              : 'bg-green-900/50 text-green-300 border border-green-700/50'
                          }`}
                        >
                          {message.type === 'user' ? (
                            <User className="w-4 h-4" />
                          ) : (
                            <Bot className="w-4 h-4" />
                          )}
                        </div>
                      </div>
                      <div className={`flex-1 max-w-[85%] ${message.type === 'user' ? 'text-right' : ''}`}>
                        <motion.div
                          initial={{ scale: 0.9 }}
                          animate={{ scale: 1 }}
                          className={`inline-block px-4 py-3 rounded-lg text-sm leading-relaxed ${
                            message.type === 'user'
                              ? 'bg-purple-600 text-white'
                              : message.type === 'agent'
                              ? 'bg-purple-900/50 text-purple-200 border border-purple-700/50'
                              : 'bg-green-900/50 text-green-200 border border-green-700/50'
                          }`}
                        >
                          <div className="whitespace-pre-wrap">{message.content}</div>
                          {message.metadata?.progress !== undefined && (
                            <div className="mt-2">
                              <Progress value={message.metadata.progress} className="h-1" />
                              <div className="flex justify-between text-xs mt-1">
                                <span>Progress</span>
                                <span>{message.metadata.progress}%</span>
                              </div>
                            </div>
                          )}
                        </motion.div>
                        <div className={`text-xs text-[#666] mt-1 ${message.type === 'user' ? 'text-right' : ''}`}>
                          {formatTime(message.timestamp)}
                          {message.agentName && (
                            <span className="ml-2 text-purple-400">@{message.agentName}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Agent Typing Indicator */}
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex gap-3"
                >
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium bg-purple-900/50 text-purple-300">
                      <Bot className="w-4 h-4" />
                    </div>
                  </div>
                  <div className="flex-1 max-w-[85%]">
                    <div className="inline-block px-4 py-3 rounded-lg text-sm bg-purple-900/30 text-purple-200">
                      <div className="flex items-center gap-2">
                        <span>AP3X is thinking</span>
                        <div className="flex gap-1">
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce"></div>
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Chat Input */}
          <div className="shrink-0 p-3 bg-[#0a0a0a] border-t border-[#1a1a1a]">
            <div className="relative bg-[#111111] rounded-xl">
              <textarea
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={`Ask ${personality.name} to help you build something amazing...`}
                className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none"
                rows={3}
                style={{ minHeight: '60px', maxHeight: '120px' }}
                disabled={isStreaming}
              />
              <button
                onClick={handleSendMessage}
                disabled={!chatInput.trim() || isStreaming}
                className="absolute right-2 top-3 w-8 h-8 bg-purple-600 hover:bg-purple-700 disabled:bg-[#333] rounded-lg flex items-center justify-center"
              >
                {isStreaming ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Send className="w-4 h-4" />
                )}
              </button>
            </div>
            
            {/* Quick Actions */}
            <div className="flex items-center gap-2 mt-2">
              <Button
                size="sm"
                variant="ghost"
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400"
                onClick={() => setShowTaskPanel(!showTaskPanel)}
              >
                <Target className="w-3 h-3 mr-1" />
                Tasks
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400"
                onClick={() => setShowAgentPanel(!showAgentPanel)}
              >
                <Users className="w-3 h-3 mr-1" />
                Agents
              </Button>
            </div>
          </div>
        </div>

        {/* Task Sidebar */}
        <AnimatePresence>
          {showTaskPanel && (
            <motion.div
              initial={{ x: 320 }}
              animate={{ x: 0 }}
              exit={{ x: 320 }}
              className="w-80 border-l border-[#1a1a1a] bg-[#0a0a0a]"
            >
              <div className="p-4 border-b border-[#1a1a1a]">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-white flex items-center gap-2">
                    <Target className="w-4 h-4 text-purple-400" />
                    AP3X Tasks
                  </h3>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                    onClick={() => setShowTaskPanel(false)}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
                <div className="mt-2">
                  <Progress 
                    value={tasks.filter(t => t.status === 'completed').length / tasks.length * 100 || 0} 
                    className="h-1" 
                  />
                  <p className="text-xs text-[#666] mt-1">
                    {tasks.filter(t => t.status === 'completed').length} / {tasks.length} completed
                  </p>
                </div>
              </div>
              
              <ScrollArea className="h-[calc(100%-120px)]">
                <div className="p-4 space-y-4">
                  {tasks.length === 0 ? (
                    <div className="text-center text-[#666] py-8">
                      <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No tasks yet</p>
                      <p className="text-xs mt-1">Ask AP3X to start planning</p>
                    </div>
                  ) : (
                    <>
                      {Object.entries(groupedTasks).map(([status, statusTasks]) => (
                        statusTasks.length > 0 && (
                          <div key={status}>
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="text-xs font-medium text-white uppercase tracking-wider">
                                {status.replace('-', ' ')}
                              </h4>
                              <span className="text-xs text-[#666]">({statusTasks.length})</span>
                            </div>
                            <div className="space-y-2">
                              {statusTasks.map((task) => (
                                <motion.div
                                  key={task.id}
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-3 hover:border-[#333]"
                                >
                                  <div className="flex items-start gap-2">
                                    <div className="mt-0.5">
                                      {getTaskIcon(task.type)}
                                    </div>
                                    <div className="flex-1">
                                      <h4 className="text-sm font-medium text-white">{task.title}</h4>
                                      <p className="text-xs text-[#666] mt-1">{task.description}</p>
                                      
                                      <div className="mt-2">
                                        <div className="flex items-center justify-between text-xs">
                                          <span className={getStatusColor(task.status)}>
                                            {getStatusIcon(task.status)} {task.status}
                                          </span>
                                          <span className="text-[#666]">{task.estimatedHours}h</span>
                                        </div>
                                        
                                        {task.status === 'in-progress' && (
                                          <div className="mt-2">
                                            <Progress value={task.progress} className="h-1" />
                                            <p className="text-xs text-[#666] mt-1">
                                              {task.progress}% complete
                                            </p>
                                          </div>
                                        )}
                                        
                                        {task.agentUpdates.length > 0 && (
                                          <div className="mt-2">
                                            <p className="text-xs text-purple-400">
                                              💡 {task.agentUpdates[task.agentUpdates.length - 1]}
                                            </p>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </motion.div>
                              ))}
                            </div>
                          </div>
                        )
                      ))}
                    </>
                  )}
                </div>
              </ScrollArea>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Agent Panel */}
        <AnimatePresence>
          {showAgentPanel && (
            <motion.div
              initial={{ x: 320 }}
              animate={{ x: 0 }}
              exit={{ x: 320 }}
              className="w-64 border-l border-[#1a1a1a] bg-[#0a0a0a]"
            >
              <div className="p-4 border-b border-[#1a1a1a]">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-white flex items-center gap-2">
                    <Users className="w-4 h-4 text-purple-400" />
                    Active Agents
                  </h3>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                    onClick={() => setShowAgentPanel(false)}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              
              <ScrollArea className="h-[calc(100%-60px)]">
                <div className="p-4 space-y-3">
                  {activeAgents.map((agent) => (
                    <motion.div
                      key={agent.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-3"
                    >
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <div>
                          <h4 className="text-sm font-medium text-white">{agent.name}</h4>
                          <p className="text-xs text-[#666]">{agent.type}</p>
                        </div>
                      </div>
                      <div className="mt-2">
                        <p className="text-xs text-[#888]">{agent.personality}</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {agent.capabilities?.slice(0, 3).map((capability: string) => (
                            <Badge key={capability} variant="outline" className="text-xs h-4 px-1">
                              {capability}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </ScrollArea>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
