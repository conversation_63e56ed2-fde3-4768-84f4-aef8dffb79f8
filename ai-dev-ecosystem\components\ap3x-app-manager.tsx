"use client"

import React, { useState } from 'react';
import { useDyadApps } from '@/hooks/use-dyad';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Plus, 
  Play, 
  Square, 
  Trash2, 
  Folder, 
  Calendar,
  Loader2,
  AlertCircle,
  RefreshCw,
  Sparkles
} from 'lucide-react';
import { App } from '@/lib/dyad-client';

interface AP3XAppManagerProps {
  onSelectApp?: (app: App) => void;
  selectedAppId?: number;
}

export default function AP3XAppManager({ onSelectApp, selectedAppId }: AP3XAppManagerProps) {
  const { apps, isLoading, error, appStatuses, createApp, deleteApp, startApp, stopApp, refreshApps, getAppStatus } = useDyadApps();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newAppName, setNewAppName] = useState('');
  const [newAppTemplate, setNewAppTemplate] = useState('react');
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateApp = async () => {
    if (!newAppName.trim()) return;

    try {
      setIsCreating(true);
      const app = await createApp(newAppName, newAppTemplate);
      setNewAppName('');
      setNewAppTemplate('react');
      setIsCreateDialogOpen(false);
      if (onSelectApp) {
        onSelectApp(app);
      }
    } catch (err) {
      console.error('Failed to create app:', err);
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteApp = async (app: App) => {
    try {
      await deleteApp(app.id);
    } catch (err) {
      console.error('Failed to delete app:', err);
    }
  };

  const handleStartApp = async (app: App) => {
    try {
      await startApp(app.id);
    } catch (err) {
      console.error('Failed to start app:', err);
    }
  };

  const handleStopApp = async (app: App) => {
    try {
      await stopApp(app.id);
    } catch (err) {
      console.error('Failed to stop app:', err);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-2 text-[#666]">
          <Loader2 className="w-4 h-4 animate-spin" />
          <span>Loading apps...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-400 mb-4">{error}</p>
          <Button 
            onClick={refreshApps} 
            variant="outline" 
            size="sm"
            className="bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold text-white flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-purple-500" />
            AP3X Apps
          </h2>
          <p className="text-sm text-[#666]">Manage your autonomous development projects</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button 
              size="sm" 
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              <Plus className="w-4 h-4 mr-2" />
              New App
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-[#0a0a0a] border-[#1a1a1a] text-white">
            <DialogHeader>
              <DialogTitle>Create New AP3X App</DialogTitle>
              <DialogDescription className="text-[#666]">
                Create a new development project with AP3X autonomous assistance
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="app-name" className="text-sm font-medium">
                  App Name
                </Label>
                <Input
                  id="app-name"
                  value={newAppName}
                  onChange={(e) => setNewAppName(e.target.value)}
                  placeholder="my-awesome-app"
                  className="bg-[#1a1a1a] border-[#333] text-white mt-1"
                />
              </div>
              <div>
                <Label htmlFor="template" className="text-sm font-medium">
                  Template
                </Label>
                <Select value={newAppTemplate} onValueChange={setNewAppTemplate}>
                  <SelectTrigger className="bg-[#1a1a1a] border-[#333] text-white mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-[#1a1a1a] border-[#333]">
                    <SelectItem value="react">React + AP3X</SelectItem>
                    <SelectItem value="nextjs">Next.js + AP3X</SelectItem>
                    <SelectItem value="vue">Vue.js + AP3X</SelectItem>
                    <SelectItem value="svelte">Svelte + AP3X</SelectItem>
                    <SelectItem value="vanilla">Vanilla JS + AP3X</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsCreateDialogOpen(false)}
                className="bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]"
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateApp}
                disabled={!newAppName.trim() || isCreating}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                {isCreating ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Creating...
                  </>
                ) : (
                  'Create App'
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Apps Grid */}
      <ScrollArea className="h-[400px]">
        {apps.length === 0 ? (
          <div className="text-center py-12">
            <Folder className="w-12 h-12 text-[#333] mx-auto mb-4" />
            <p className="text-[#666] mb-2">No apps yet</p>
            <p className="text-sm text-[#555]">Create your first AP3X app to get started</p>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {apps.map((app) => (
              <Card 
                key={app.id} 
                className={`bg-[#111111] border-[#1a1a1a] hover:border-[#333] transition-colors cursor-pointer ${
                  selectedAppId === app.id ? 'border-purple-500 bg-[#0a0a1a]' : ''
                }`}
                onClick={() => onSelectApp?.(app)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-sm font-medium text-white truncate">
                      {app.name}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      {(() => {
                        const status = getAppStatus(app.id);
                        return status.isRunning ? (
                          <Badge className="bg-green-600 text-white text-xs">
                            Running
                          </Badge>
                        ) : (
                          <Badge variant="secondary" className="bg-[#1a1a1a] text-[#888] text-xs">
                            Stopped
                          </Badge>
                        );
                      })()}
                      <Badge variant="secondary" className="bg-[#1a1a1a] text-[#888] text-xs">
                        {app.id}
                      </Badge>
                    </div>
                  </div>
                  <CardDescription className="text-xs text-[#666] flex items-center gap-1">
                    <Calendar className="w-3 h-3" />
                    {formatDate(app.createdAt)}
                    {(() => {
                      const status = getAppStatus(app.id);
                      return status.isRunning && status.url ? (
                        <span className="ml-2 text-purple-400">
                          • {status.url}
                        </span>
                      ) : null;
                    })()}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {(() => {
                        const status = getAppStatus(app.id);
                        return status.isRunning ? (
                          <>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStopApp(app);
                              }}
                              className="h-7 px-2 bg-red-600/20 border-red-600/50 text-red-400 hover:text-red-300 hover:bg-red-600/30"
                            >
                              <Square className="w-3 h-3" />
                            </Button>
                            {status.url && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  window.open(status.url!, '_blank');
                                }}
                                className="h-7 px-2 bg-purple-600/20 border-purple-600/50 text-purple-400 hover:text-purple-300 hover:bg-purple-600/30"
                              >
                                Open
                              </Button>
                            )}
                          </>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleStartApp(app);
                            }}
                            className="h-7 px-2 bg-green-600/20 border-green-600/50 text-green-400 hover:text-green-300 hover:bg-green-600/30"
                          >
                            <Play className="w-3 h-3" />
                          </Button>
                        );
                      })()}
                    </div>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => e.stopPropagation()}
                          className="h-7 px-2 bg-[#1a1a1a] border-[#333] text-red-400 hover:text-red-300 hover:bg-red-900/20"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent className="bg-[#0a0a0a] border-[#1a1a1a] text-white">
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete App</AlertDialogTitle>
                          <AlertDialogDescription className="text-[#666]">
                            Are you sure you want to delete "{app.name}"? This action cannot be undone.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel className="bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]">
                            Cancel
                          </AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteApp(app)}
                            className="bg-red-600 hover:bg-red-700 text-white"
                          >
                            Delete
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
