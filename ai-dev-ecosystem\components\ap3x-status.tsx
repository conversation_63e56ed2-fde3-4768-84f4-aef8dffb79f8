"use client"

import React, { useState, useEffect } from 'react';
import { ap3xClient } from '@/lib/ap3x-client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw, CheckCircle, AlertCircle, Zap, Bot, Database } from 'lucide-react';

export default function AP3XStatus() {
  const [status, setStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const checkStatus = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const health = await ap3xClient.getHealth();
      setStatus(health);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Connection failed');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkStatus();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="flex items-center gap-2 text-[#666]">
          <RefreshCw className="w-4 h-4 animate-spin" />
          <span>Connecting to AP3X...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
        <p className="text-red-400 mb-4">{error}</p>
        <Button 
          onClick={checkStatus} 
          variant="outline" 
          size="sm"
          className="bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-white">AP3X Status</h3>
        <Button
          onClick={checkStatus}
          variant="ghost"
          size="sm"
          className="h-6 px-2 text-[#666] hover:text-white"
        >
          <RefreshCw className="w-3 h-3" />
        </Button>
      </div>

      {status && (
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span className="text-sm text-green-400">AP3X Connected</span>
          </div>

          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="bg-[#111111] p-3 rounded-lg border border-[#1a1a1a]">
              <div className="flex items-center gap-2 mb-1">
                <Bot className="w-3 h-3 text-purple-400" />
                <span className="text-[#666]">Agents</span>
              </div>
              <div className="text-white font-medium">
                {status.ap3xFramework?.activeAgents || 0} / {status.ap3xFramework?.agents || 0}
              </div>
            </div>

            <div className="bg-[#111111] p-3 rounded-lg border border-[#1a1a1a]">
              <div className="flex items-center gap-2 mb-1">
                <Zap className="w-3 h-3 text-yellow-400" />
                <span className="text-[#666]">Status</span>
              </div>
              <div className="text-white font-medium capitalize">
                {status.status || 'unknown'}
              </div>
            </div>

            <div className="bg-[#111111] p-3 rounded-lg border border-[#1a1a1a]">
              <div className="flex items-center gap-2 mb-1">
                <Database className="w-3 h-3 text-blue-400" />
                <span className="text-[#666]">Context</span>
              </div>
              <div className="text-white font-medium">
                {status.contextEngine?.nodes || 0} nodes
              </div>
            </div>

            <div className="bg-[#111111] p-3 rounded-lg border border-[#1a1a1a]">
              <div className="flex items-center gap-2 mb-1">
                <CheckCircle className="w-3 h-3 text-green-400" />
                <span className="text-[#666]">Success Rate</span>
              </div>
              <div className="text-white font-medium">
                {status.ap3xFramework?.successfulExecutions || 0}%
              </div>
            </div>
          </div>

          <div className="text-xs text-[#666]">
            Last updated: {new Date(status.timestamp).toLocaleTimeString()}
          </div>
        </div>
      )}
    </div>
  );
}
