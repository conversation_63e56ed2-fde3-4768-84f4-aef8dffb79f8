"use client"

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Bo<PERSON>, User, Plus, CheckCircle, Clock, AlertCircle, Sparkles, Code, FileText, Zap, Target, Users, Send, X, Brain, Activity, MessageSquare, Play, Pause, RotateCcw, ChevronDown, ChevronUp, Eye, EyeOff } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { ap3xClient } from '@/lib/ap3x-client';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useSocket } from '@/hooks/use-socket';

interface AP3XCollaborativeChatProps {
  selectedApp?: any;
}

interface AP3XTask {
  id: string;
  title: string;
  description: string;
  type: string;
  status: 'pending' | 'in-progress' | 'completed' | 'blocked';
  progress: number;
  estimatedHours: number;
  agentUpdates: string[];
  startedAt?: string;
  completedAt?: string;
  assignedAgent?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

interface AgentMessage {
  id: string;
  type: 'user' | 'agent' | 'task-update' | 'progress' | 'insight' | 'code-suggestion' | 'status-update';
  content: string;
  metadata?: any;
  timestamp: string;
  agentName?: string;
  agentStatus?: string;
  codeSnippet?: string;
  filePath?: string;
}

interface AgentPersonality {
  name: string;
  tone: string;
  greeting: string;
  avatar: string;
  color: string;
  personality: string;
}

// Enhanced agent personalities with more collaborative traits
const agentPersonalities: Record<string, AgentPersonality> = {
  collaborative: {
    name: 'Collaborative Partner',
    tone: 'friendly and supportive',
    greeting: "Hey there! I'm your development partner. Let's build something amazing together! 🤝 I'll be here every step of the way, offering suggestions and keeping you updated on our progress.",
    avatar: '🤝',
    color: 'purple',
    personality: 'Supportive and encouraging, always ready to brainstorm and provide constructive feedback.'
  },
  expert: {
    name: 'Expert Consultant',
    tone: 'professional and precise',
    greeting: "Greetings! I'm here to provide expert guidance and ensure we build with best practices. 🎯 I'll analyze your requirements and suggest optimal solutions based on industry standards.",
    avatar: '🎯',
    color: 'blue',
    personality: 'Analytical and methodical, focused on best practices and technical excellence.'
  },
  creative: {
    name: 'Creative Collaborator',
    tone: 'innovative and enthusiastic',
    greeting: "What's up! Ready to create something innovative? Let's think outside the box! ✨ I'll help you explore creative solutions and modern approaches.",
    avatar: '✨',
    color: 'pink',
    personality: 'Innovative and enthusiastic, always exploring new possibilities and creative solutions.'
  },
  mentor: {
    name: 'Mentor Guide',
    tone: 'patient and educational',
    greeting: "Hello! I'll guide you through each step and help you learn along the way. 📚 I'll explain my decisions and teach you the reasoning behind each approach.",
    avatar: '📚',
    color: 'green',
    personality: 'Patient and educational, focused on teaching and explaining the why behind technical decisions.'
  }
};

// Real-time status indicators
const AgentStatusIndicator = ({ status }: { status: string }) => {
  const statusConfig = {
    active: { color: 'bg-green-500', pulse: true, label: 'Active' },
    thinking: { color: 'bg-yellow-500', pulse: true, label: 'Thinking' },
    working: { color: 'bg-blue-500', pulse: true, label: 'Working' },
    idle: { color: 'bg-gray-500', pulse: false, label: 'Idle' },
    busy: { color: 'bg-orange-500', pulse: true, label: 'Busy' }
  };

  const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.idle;

  return (
    <div className="flex items-center gap-2">
      <div className={`w-2 h-2 rounded-full ${config.color} ${config.pulse ? 'animate-pulse' : ''}`} />
      <span className="text-xs text-[#666]">{config.label}</span>
    </div>
  );
};

// Progress visualization component
const TaskProgress = ({ task }: { task: AP3XTask }) => {
  const getProgressColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'in-progress': return 'bg-blue-500';
      case 'blocked': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-white">{task.title}</span>
        <span className="text-xs text-[#666]">{task.progress}%</span>
      </div>
      <div className="w-full bg-[#1a1a1a] rounded-full h-2">
        <motion.div
          className={`h-2 rounded-full ${getProgressColor(task.status)}`}
          initial={{ width: 0 }}
          animate={{ width: `${task.progress}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>
      {task.agentUpdates.length > 0 && (
        <div className="text-xs text-purple-400">
          💡 {task.agentUpdates[task.agentUpdates.length - 1]}
        </div>
      )}
    </div>
  );
};

// Code suggestion component
const CodeSuggestion = ({ suggestion }: { suggestion: AgentMessage }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-4"
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <Code className="w-4 h-4 text-purple-400" />
          <span className="text-sm font-medium text-white">Code Suggestion</span>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-[#666] hover:text-white"
        >
          {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
        </button>
      </div>
      
      <p className="text-sm text-[#ccc] mb-2">{suggestion.content}</p>
      
      {isExpanded && suggestion.codeSnippet && (
        <motion.div
          initial={{ height: 0 }}
          animate={{ height: 'auto' }}
          className="overflow-hidden"
        >
          <pre className="bg-[#0a0a0a] border border-[#333] rounded p-3 text-sm text-[#ccc] overflow-x-auto">
            <code>{suggestion.codeSnippet}</code>
          </pre>
          {suggestion.filePath && (
            <p className="text-xs text-[#666] mt-1">Suggested for: {suggestion.filePath}</p>
          )}
        </motion.div>
      )}
    </motion.div>
  );
};

export default function AP3XCollaborativeChat({ selectedApp }: AP3XCollaborativeChatProps) {
  const [chatInput, setChatInput] = useState('');
  const [agentPersonality, setAgentPersonality] = useState('collaborative');
  const [showTaskPanel, setShowTaskPanel] = useState(true);
  const [showAgentPanel, setShowAgentPanel] = useState(true);
  const [isTyping, setIsTyping] = useState(false);
  const [currentAgentStatus, setCurrentAgentStatus] = useState('idle');
  const [streamingContent, setStreamingContent] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();
  const socket = useSocket();

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Real-time updates via WebSocket
  useEffect(() => {
    if (!socket || !selectedApp) return;

    const handleAgentMessage = (data: any) => {
      if (data.appId === selectedApp.id) {
        queryClient.invalidateQueries(['conversation', selectedApp.id]);
      }
    };

    const handleTaskUpdated = (data: any) => {
      if (data.appId === selectedApp.id) {
        queryClient.invalidateQueries(['tasks', selectedApp.id]);
      }
    };

    const handleAgentStatus = (data: any) => {
      if (data.appId === selectedApp.id) {
        setCurrentAgentStatus(data.status);
      }
    };

    socket.on('agentMessage', handleAgentMessage);
    socket.on('taskUpdated', handleTaskUpdated);
    socket.on('agentStatus', handleAgentStatus);

    return () => {
      socket.off('agentMessage', handleAgentMessage);
      socket.off('taskUpdated', handleTaskUpdated);
      socket.off('agentStatus', handleAgentStatus);
    };
  }, [socket, selectedApp, queryClient]);

  // Fetch conversation history
  const { data: conversation = [], isLoading: isLoadingConversation } = useQuery({
    queryKey: ['conversation', selectedApp?.id],
    queryFn: () => selectedApp ? ap3xClient.getAgentConversation(selectedApp.id) : Promise.resolve([]),
    enabled: !!selectedApp,
    refetchInterval: 2000,
  });

  // Fetch tasks
  const { data: taskData = { tasks: [], activeAgents: [] }, isLoading: isLoadingTasks } = useQuery({
    queryKey: ['tasks', selectedApp?.id],
    queryFn: () => selectedApp ? ap3xClient.getTaskUpdates(selectedApp.id) : Promise.resolve({ tasks: [], activeAgents: [] }),
    enabled: !!selectedApp,
    refetchInterval: 1000,
  });

  // Fetch agents
  const { data: agents = [] } = useQuery({
    queryKey: ['agents'],
    queryFn: () => ap3xClient.getAgents(),
    refetchInterval: 5000,
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: ({ message, agentType }: { message: string; agentType?: string }) =>
      ap3xClient.sendAgentMessage(selectedApp!.id, message, agentType),
    onSuccess: () => {
      queryClient.invalidateQueries(['conversation', selectedApp?.id]);
    },
  });

  // Create task plan mutation
  const createTaskPlanMutation = useMutation({
    mutationFn: ({ featureRequest, priority }: { featureRequest: string; priority?: string }) =>
      ap3xClient.createTaskPlan(selectedApp!.id, featureRequest, priority),
    onSuccess: (data) => {
      queryClient.invalidateQueries(['tasks', selectedApp?.id]);
    },
  });

  // Stream agent updates
  const streamAgentUpdates = useCallback(async () => {
    if (!selectedApp) return;
    
    setIsStreaming(true);
    setStreamingContent('');
    
    try {
      await ap3xClient.streamAgentUpdates(selectedApp.id, (update: any) => {
        if (update.type === 'progress') {
          setStreamingContent(prev => prev + update.content);
        }
      });
    } catch (error) {
      console.error('Streaming error:', error);
    } finally {
      setIsStreaming(false);
      setStreamingContent('');
    }
  }, [selectedApp]);

  const handleSendMessage = async () => {
    if (!chatInput.trim() || !selectedApp) return;

    const message = chatInput;
    setChatInput('');
    setIsTyping(true);

    try {
      await sendMessageMutation.mutateAsync({ message });
      
      // If the message contains keywords like "plan" or "create", trigger task planning
      if (message.toLowerCase().includes('plan') || message.toLowerCase().includes('create')) {
        await createTaskPlanMutation.mutateAsync({ 
          featureRequest: message,
          priority: 'medium'
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const groupedTasks = useMemo(() => {
    const grouped: Record<string, AP3XTask[]> = {
      pending: [],
      'in-progress': [],
      completed: [],
      blocked: []
    };
    
    taskData.tasks.forEach(task => {
      grouped[task.status]?.push(task);
    });
    
    return grouped;
  }, [taskData.tasks]);

  const personality = agentPersonalities[agentPersonality];

  if (!selectedApp) {
    return (
      <div className="h-full flex items-center justify-center text-[#666]">
        <div className="text-center">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            <Bot className="w-16 h-16 mx-auto mb-4 opacity-50" />
          </motion.div>
          <p className="text-lg mb-2">Welcome to AP3X AI Development</p>
          <p className="text-sm text-[#888]">Select an app to start collaborating with your AI development partner</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-[#0a0a0a] to-[#111111] rounded-xl overflow-hidden border border-[#1a1a1a]">
      {/* Header with real-time status */}
      <div className="px-6 py-4 border-b border-[#1a1a1a] flex items-center justify-between bg-[#0a0a0a]/50">
        <div className="flex items-center gap-3">
          <div className="relative">
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-3 h-3 bg-purple-500 rounded-full"
            />
            <motion.div
              animate={{ scale: [1, 1.5, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="absolute inset-0 w-3 h-3 bg-purple-500 rounded-full opacity-50"
            />
          </div>
          <div>
            <h2 className="font-medium text-white text-sm">
              {selectedApp.name} - {personality.name}
            </h2>
            <div className="flex items-center gap-2">
              <AgentStatusIndicator status={currentAgentStatus} />
              <span className="text-xs text-[#666]">{personality.tone}</span>
            </div>
          </div>
          {taskData.activeAgents.length > 0 && (
            <span className="text-xs bg-purple-900/20 text-purple-400 px-2 py-1 rounded">
              {taskData.activeAgents.length} agents active
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowTaskPanel(!showTaskPanel)}
            className="h-7 px-2 bg-[#1a1a1a] border border-[#333] text-[#888] hover:text-white rounded text-xs flex items-center gap-1"
          >
            <Target className="w-3 h-3" />
            Tasks
          </button>
          <button
            onClick={() => setShowAgentPanel(!showAgentPanel)}
            className="h-7 px-2 bg-[#1a1a1a] border border-[#333] text-[#888] hover:text-white rounded text-xs flex items-center gap-1"
          >
            <Users className="w-3 h-3" />
            Agents
          </button>
        </div>
      </div>

      <div className="flex-1 flex min-h-0">
        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <div className="flex-1 overflow-y-auto">
            <div className="px-6 py-4 space-y-4">
              <AnimatePresence>
                {conversation.map((message: AgentMessage) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="group"
                  >
                    <div className={`flex gap-3 ${message.type === 'user' ? 'flex-row-reverse' : ''}`}>
                      <div className="flex-shrink-0">
                        <motion.div
                          whileHover={{ scale: 1.1 }}
                          className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                            message.type === 'user'
                              ? 'bg-purple-600 text-white'
                              : message.type === 'agent'
                              ? 'bg-purple-900/50 text-purple-300 border border-purple-700/50'
                              : message.type === 'code-suggestion'
                              ? 'bg-green-900/50 text-green-300 border border-green-700/50'
                              : 'bg-blue-900/50 text-blue-300 border border-blue-700/50'
                          }`}
                        >
                          {message.type === 'user' ? (
                            <User className="w-4 h-4" />
                          ) : message.type === 'code-suggestion' ? (
                            <Code className="w-4 h-4" />
                          ) : (
                            <Bot className="w-4 h-4" />
                          )}
                        </motion.div>
                      </div>
                      <div className={`flex-1 max-w-[85%] ${message.type === 'user' ? 'text-right' : ''}`}>
                        <motion.div
                          initial={{ scale: 0.9 }}
                          animate={{ scale: 1 }}
                          className={`inline-block px-4 py-3 rounded-lg text-sm leading-relaxed ${
                            message.type === 'user'
                              ? 'bg-purple-600 text-white'
                              : message.type === 'agent'
                              ? 'bg-purple-900/50 text-purple-200 border border-purple-700/50'
                              : message.type === 'code-suggestion'
                              ? 'bg-green-900/50 text-green-200 border border-green-700/50'
                              : 'bg-blue-900/50 text-blue-200 border border-blue-700/50'
                          }`}
                        >
                          <div className="whitespace-pre-wrap">{message.content}</div>
                          {message.codeSnippet && (
                            <div className="mt-2">
                              <pre className="bg-[#0a0a0a] border border-[#333] rounded p-2 text-xs text-[#ccc] overflow-x-auto">
                                <code>{message.codeSnippet}</code>
                              </pre>
                            </div>
                          )}
                        </motion.div>
                        <div className={`text-xs text-[#666] mt-1 ${message.type === 'user' ? 'text-right' : ''}`}>
                          {formatTime(message.timestamp)}
                          {message.agentName && (
                            <span className="ml-2 text-purple-400">@{message.agentName}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}

                {/* Streaming content */}
                {isStreaming && streamingContent && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex gap-3"
                  >
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium bg-purple-900/50 text-purple-300">
                        <Brain className="w-4 h-4" />
                      </div>
                    </div>
                    <div className="flex-1 max-w-[85%]">
                      <div className="inline-block px-4 py-3 rounded-lg text-sm bg-purple-900/30 text-purple-200">
                        <div className="flex items-center gap-2">
                          <span>AP3X is processing...</span>
                          <div className="flex gap-1">
                            <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce"></div>
                            <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                            <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                          </div>
                        </div>
                        {streamingContent && (
                          <div className="mt-2 text-xs text-[#888]">{streamingContent}</div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Agent Typing Indicator */}
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex gap-3"
                >
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium bg-purple-900/50 text-purple-300">
                      <Bot className="w-4 h-4" />
                    </div>
                  </div>
                  <div className="flex-1 max-w-[85%]">
                    <div className="inline-block px-4 py-3 rounded-lg text-sm bg-purple-900/30 text-purple-200">
                      <div className="flex items-center gap-2">
                        <span>AP3X is thinking</span>
                        <div className="flex gap-1">
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce"></div>
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                          <div className="w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </div>

          {/* Quick Actions Bar */}
          <div className="shrink-0 p-3 bg-[#0a0a0a] border-t border-[#1a1a1a]">
            <div className="flex items-center gap-2 mb-2">
              <button
                onClick={() => setShowTaskPanel(!showTaskPanel)}
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400 flex items-center gap-1"
              >
                <Target className="w-3 h-3" />
                Tasks ({taskData.tasks.length})
              </button>
              <button
                onClick={() => setShowAgentPanel(!showAgentPanel)}
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400 flex items-center gap-1"
              >
                <Users className="w-3 h-3" />
                Agents ({agents.length})
              </button>
              <button
                onClick={streamAgentUpdates}
                className="h-6 px-2 text-xs text-[#666] hover:text-purple-400 flex items-center gap-1"
              >
                <Activity className="w-3 h-3" />
                Stream Updates
              </button>
            </div>

            {/* Chat Input */}
            <div className="relative bg-[#111111] rounded-xl">
              <textarea
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={`Ask ${personality.name} to help you build something amazing...`}
                className="w-full bg-transparent px-4 py-3 pr-12 text-sm text-white placeholder:text-[#666] resize-none focus:outline-none"
                rows={3}
                style={{ minHeight: '60px', maxHeight: '120px' }}
              />
              <button
                onClick={handleSendMessage}
                disabled={!chatInput.trim() || sendMessageMutation.isPending}
                className="absolute right-2 top-3 w-8 h-8 bg-purple-600 hover:bg-purple-700 disabled:bg-[#333] rounded-lg flex items-center justify-center"
              >
                <Send className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Task Sidebar with Real-time Updates */}
        <AnimatePresence>
          {showTaskPanel && (
            <motion.div
              initial={{ x: 320 }}
              animate={{ x: 0 }}
              exit={{ x: 320 }}
              className="w-80 border-l border-[#1a1a1a] bg-[#0a0a0a]"
            >
              <div className="p-4 border-b border-[#1a1a1a]">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-white flex items-center gap-2">
                    <Target className="w-4 h-4 text-purple-400" />
                    AP3X Tasks
                  </h3>
                  <button
                    onClick={() => setShowTaskPanel(false)}
                    className="h-6 w-6 p-0 text-[#666] hover:text-white"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
                <div className="mt-2">
                  <div className="w-full bg-[#1a1a1a] rounded-full h-1">
                    <motion.div 
                      className="bg-purple-500 h-1 rounded-full" 
                      initial={{ width: 0 }}
                      animate={{ width: `${taskData.tasks.filter(t => t.status === 'completed').length / taskData.tasks.length * 100 || 0}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>
                  <p className="text-xs text-[#666] mt-1">
                    {taskData.tasks.filter(t => t.status === 'completed').length} / {taskData.tasks.length} completed
                  </p>
                </div>
              </div>
              
              <div className="h-[calc(100%-120px)] overflow-y-auto">
                <div className="p-4 space-y-4">
                  {taskData.tasks.length === 0 ? (
                    <div className="text-center text-[#666] py-8">
                      <motion.div
                        animate={{ rotate: [0, 360] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        <Sparkles className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      </motion.div>
                      <p className="text-sm">No tasks yet</p>
                      <p className="text-xs mt-1">Ask AP3X to start planning</p>
                    </div>
                  ) : (
                    <>
                      {Object.entries(groupedTasks).map(([status, statusTasks]) => (
                        statusTasks.length > 0 && (
                          <div key={status}>
                            <div className="flex items-center gap-2 mb-2">
                              <h4 className="text-xs font-medium text-white uppercase tracking-wider">
                                {status.replace('-', ' ')}
                              </h4>
                              <span className="text-xs text-[#666]">({statusTasks.length})</span>
                            </div>
                            <div className="space-y-2">
                              {statusTasks.map((task) => (
                                <motion.div
                                  key={task.id}
                                  initial={{ opacity: 0, y: 10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-3 hover:border-[#333]"
                                >
                                  <TaskProgress task={task} />
                                </motion.div>
                              ))}
                            </div>
                          </div>
                        )
                      ))}
                    </>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Agent Panel with Real-time Status */}
        <AnimatePresence>
          {showAgentPanel && (
            <motion.div
              initial={{ x: 320 }}
              animate={{ x: 0 }}
              exit={{ x: 320 }}
              className="w-64 border-l border-[#1a1a1a] bg-[#0a0a0a]"
            >
              <div className="p-4 border-b border-[#1a1a1a]">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium text-white flex items-center gap-2">
                    <Users className="w-4 h-4 text-purple-400" />
                    Active Agents
                  </h3>
                  <button
                    onClick={() => setShowAgentPanel(false)}
                    className="h-6 w-6 p-0 text-[#666] hover:text-white"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              </div>
              
              <div className="h-[calc(100%-60px)] overflow-y-auto">
                <div className="p-4 space-y-3">
                  {agents.map((agent) => (
                    <motion.div
                      key={agent.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      whileHover={{ scale: 1.02 }}
                      className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-3"
                    >
                      <div className="flex items-center gap-2 mb-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <div>
                          <h4 className="text-sm font-medium text-white">{agent.name}</h4>
                          <p className="text-xs text-[#666]">{agent.type}</p>
                        </div>
                      </div>
                      <div className="mt-2">
                        <p className="text-xs text-[#888]">{agent.personality}</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {agent.capabilities?.slice(0, 3).map((capability: string) => (
                            <span key={capability} className="text-xs bg-[#1a1a1a] text-[#666] px-1 py-0.5 rounded">
                              {capability}
                            </span>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
